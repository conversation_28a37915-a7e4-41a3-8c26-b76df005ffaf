import React, { useState, useMemo } from 'react';
import React<PERSON><PERSON>, {
  Background,
  Controls,
  MiniMap,
  ReactFlowProvider,
  Node,
  Edge,
  NodeTypes,
  EdgeTypes,
  ConnectionLineType,
} from 'reactflow';
import 'reactflow/dist/style.css';

import '@/shared/styles/react-flow.css';
import ComponentDemo from '@/modules/components/components/ComponentDemo';
import { Card, Container, Typography, Tabs, Select } from '@/shared/components/common';
import {
  CardNode,
  ProcessNode,
  DecisionNode,
  StartEndNode,
  CustomEdge,
  StepEdge,
} from '@/shared/flow';

// Đ<PERSON>nh nghĩa các node types
const nodeTypes: NodeTypes = {
  cardNode: CardNode,
  processNode: ProcessNode,
  decisionNode: DecisionNode,
  startEndNode: StartEndNode,
};

// Định nghĩa các edge types
const edgeTypes: EdgeTypes = {
  customEdge: CustomEdge,
  stepEdge: StepEdge,
};

// <PERSON>ữ liệu mẫu cho các node
const initialNodes: Node[] = [
  // Card Nodes
  {
    id: 'card-1',
    type: 'cardNode',
    position: { x: 100, y: 100 },
    data: {
      title: 'Card Node 1',
      description: '<PERSON><PERSON> tả cho card node 1',
      icon: 'file-text',
      extraData: {
        'Trạng thái': 'Hoạt động',
        'Ngày tạo': '01/01/2023',
      },
    },
    draggable: true,
  },
  {
    id: 'card-2',
    type: 'cardNode',
    position: { x: 400, y: 100 },
    data: {
      title: 'Card Node 2',
      description: 'Mô tả cho card node 2',
      icon: 'mail',
    },
    draggable: true,
  },

  // Process Nodes
  {
    id: 'process-1',
    type: 'processNode',
    position: { x: 100, y: 300 },
    data: {
      title: 'Process Node 1',
      description: 'Đang xử lý...',
      status: 'processing',
    },
    draggable: true,
  },
  {
    id: 'process-2',
    type: 'processNode',
    position: { x: 400, y: 300 },
    data: {
      title: 'Process Node 2',
      description: 'Đã hoàn thành',
      status: 'completed',
    },
    draggable: true,
  },

  // Decision Node
  {
    id: 'decision-1',
    type: 'decisionNode',
    position: { x: 250, y: 500 },
    data: {
      question: 'Có tiếp tục không?',
      description: 'Quyết định tiếp theo',
      yesLabel: 'Có',
      noLabel: 'Không',
    },
    draggable: true,
  },

  // Start/End Nodes
  {
    id: 'start-1',
    type: 'startEndNode',
    position: { x: 100, y: 700 },
    data: {
      type: 'start',
      label: 'Bắt đầu',
    },
    draggable: true,
  },
  {
    id: 'end-1',
    type: 'startEndNode',
    position: { x: 400, y: 700 },
    data: {
      type: 'end',
      label: 'Kết thúc',
    },
    draggable: true,
  },
];

// Dữ liệu mẫu cho các edge
const initialEdges: Edge[] = [
  // Custom Edges
  {
    id: 'edge-1',
    source: 'card-1',
    target: 'card-2',
    type: 'customEdge',
    data: {
      label: 'Kết nối 1',
      color: '#3b82f6',
      animated: true,
    },
  },
  {
    id: 'edge-2',
    source: 'process-1',
    target: 'process-2',
    type: 'customEdge',
    data: {
      label: 'Kết nối 2',
      color: '#10b981',
      strokeWidth: 2,
      strokeType: 'dashed',
    },
  },

  // Step Edges
  {
    id: 'edge-3',
    source: 'process-2',
    target: 'decision-1',
    type: 'stepEdge',
    data: {
      label: 'Tiếp theo',
      color: '#6366f1',
      strokeWidth: 2,
    },
  },
  {
    id: 'edge-4',
    source: 'decision-1',
    target: 'start-1',
    type: 'stepEdge',
    sourceHandle: 'no',
    data: {
      label: 'Quay lại',
      color: '#ef4444',
      strokeType: 'dotted',
    },
  },
  {
    id: 'edge-5',
    source: 'decision-1',
    target: 'end-1',
    type: 'stepEdge',
    sourceHandle: 'yes',
    data: {
      label: 'Hoàn thành',
      color: '#22c55e',
    },
  },
];

// Các ví dụ flow
const flowExamples = {
  basic: {
    name: 'Cơ bản',
    nodes: initialNodes,
    edges: initialEdges,
  },
  workflow: {
    name: 'Quy trình xử lý yêu cầu',
    nodes: [
      {
        id: 'start',
        type: 'startEndNode',
        position: { x: 250, y: 50 },
        data: { type: 'start', label: 'Bắt đầu' },
        draggable: true,
      },
      {
        id: 'process-1',
        type: 'processNode',
        position: { x: 250, y: 150 },
        data: { title: 'Nhận yêu cầu', status: 'completed' },
        draggable: true,
      },
      {
        id: 'process-2',
        type: 'processNode',
        position: { x: 250, y: 250 },
        data: { title: 'Xử lý yêu cầu', status: 'processing' },
        draggable: true,
      },
      {
        id: 'decision',
        type: 'decisionNode',
        position: { x: 250, y: 350 },
        data: { question: 'Yêu cầu hợp lệ?', yesLabel: 'Có', noLabel: 'Không' },
        draggable: true,
      },
      {
        id: 'process-3',
        type: 'processNode',
        position: { x: 400, y: 450 },
        data: { title: 'Phê duyệt yêu cầu', status: 'pending' },
        draggable: true,
      },
      {
        id: 'process-4',
        type: 'processNode',
        position: { x: 100, y: 450 },
        data: { title: 'Từ chối yêu cầu', status: 'error' },
        draggable: true,
      },
      {
        id: 'end',
        type: 'startEndNode',
        position: { x: 250, y: 550 },
        data: { type: 'end', label: 'Kết thúc' },
        draggable: true,
      },
    ],
    edges: [
      { id: 'e1', source: 'start', target: 'process-1', type: 'stepEdge' },
      { id: 'e2', source: 'process-1', target: 'process-2', type: 'stepEdge' },
      { id: 'e3', source: 'process-2', target: 'decision', type: 'stepEdge' },
      {
        id: 'e4',
        source: 'decision',
        target: 'process-3',
        sourceHandle: 'yes',
        type: 'customEdge',
        data: { color: '#22c55e', animated: true },
      },
      {
        id: 'e5',
        source: 'decision',
        target: 'process-4',
        sourceHandle: 'no',
        type: 'customEdge',
        data: { color: '#ef4444', animated: true },
      },
      { id: 'e6', source: 'process-3', target: 'end', type: 'stepEdge' },
      { id: 'e7', source: 'process-4', target: 'end', type: 'stepEdge' },
    ],
  },
  organization: {
    name: 'Sơ đồ tổ chức',
    nodes: [
      {
        id: 'ceo',
        type: 'cardNode',
        position: { x: 250, y: 50 },
        data: {
          title: 'CEO',
          description: 'Giám đốc điều hành',
          icon: 'user',
          extraData: {
            'Phòng ban': 'Ban lãnh đạo',
            'Nhân viên': '1',
          },
        },
        draggable: true,
      },
      {
        id: 'cto',
        type: 'cardNode',
        position: { x: 100, y: 200 },
        data: {
          title: 'CTO',
          description: 'Giám đốc công nghệ',
          icon: 'code',
          extraData: {
            'Phòng ban': 'Công nghệ',
            'Nhân viên': '1',
          },
        },
        draggable: true,
      },
      {
        id: 'cfo',
        type: 'cardNode',
        position: { x: 250, y: 200 },
        data: {
          title: 'CFO',
          description: 'Giám đốc tài chính',
          icon: 'dollar-sign',
          extraData: {
            'Phòng ban': 'Tài chính',
            'Nhân viên': '1',
          },
        },
        draggable: true,
      },
      {
        id: 'cmo',
        type: 'cardNode',
        position: { x: 400, y: 200 },
        data: {
          title: 'CMO',
          description: 'Giám đốc marketing',
          icon: 'trending-up',
          extraData: {
            'Phòng ban': 'Marketing',
            'Nhân viên': '1',
          },
        },
        draggable: true,
      },
      {
        id: 'dev-lead',
        type: 'cardNode',
        position: { x: 50, y: 350 },
        data: {
          title: 'Dev Lead',
          description: 'Trưởng nhóm phát triển',
          icon: 'code',
          extraData: {
            'Phòng ban': 'Công nghệ',
            'Nhân viên': '1',
          },
        },
        draggable: true,
      },
      {
        id: 'qa-lead',
        type: 'cardNode',
        position: { x: 150, y: 350 },
        data: {
          title: 'QA Lead',
          description: 'Trưởng nhóm kiểm thử',
          icon: 'check-square',
          extraData: {
            'Phòng ban': 'Công nghệ',
            'Nhân viên': '1',
          },
        },
        draggable: true,
      },
    ],
    edges: [
      {
        id: 'e1',
        source: 'ceo',
        target: 'cto',
        type: 'customEdge',
        data: { animated: true },
      },
      {
        id: 'e2',
        source: 'ceo',
        target: 'cfo',
        type: 'customEdge',
        data: { animated: true },
      },
      {
        id: 'e3',
        source: 'ceo',
        target: 'cmo',
        type: 'customEdge',
        data: { animated: true },
      },
      {
        id: 'e4',
        source: 'cto',
        target: 'dev-lead',
        type: 'customEdge',
        data: { animated: true },
      },
      {
        id: 'e5',
        source: 'cto',
        target: 'qa-lead',
        type: 'customEdge',
        data: { animated: true },
      },
    ],
  },
  okr: {
    name: 'Sơ đồ OKR',
    nodes: [
      {
        id: 'objective',
        type: 'cardNode',
        position: { x: 250, y: 50 },
        data: {
          title: 'Mục tiêu chính',
          description: 'Tăng doanh thu 30% trong Q3',
          icon: 'target',
          extraData: {
            Loại: 'Objective',
            Quý: 'Q3/2023',
          },
        },
        draggable: true,
      },
      {
        id: 'kr1',
        type: 'processNode',
        position: { x: 100, y: 200 },
        data: {
          title: 'KR1: Tăng số lượng khách hàng mới',
          description: 'Mục tiêu: 500 khách hàng mới',
          status: 'processing',
        },
        draggable: true,
      },
      {
        id: 'kr2',
        type: 'processNode',
        position: { x: 400, y: 200 },
        data: {
          title: 'KR2: Tăng giá trị đơn hàng trung bình',
          description: 'Mục tiêu: $200/đơn hàng',
          status: 'completed',
        },
        draggable: true,
      },
      {
        id: 'kr3',
        type: 'processNode',
        position: { x: 250, y: 200 },
        data: {
          title: 'KR3: Giảm tỷ lệ hủy đơn hàng',
          description: 'Mục tiêu: <5%',
          status: 'pending',
        },
        draggable: true,
      },
      {
        id: 'task1',
        type: 'cardNode',
        position: { x: 100, y: 350 },
        data: {
          title: 'Chiến dịch marketing mới',
          description: 'Triển khai chiến dịch trên Facebook và Google',
          icon: 'activity',
        },
        draggable: true,
      },
      {
        id: 'task2',
        type: 'cardNode',
        position: { x: 250, y: 350 },
        data: {
          title: 'Cải thiện trải nghiệm người dùng',
          description: 'Tối ưu hóa quy trình thanh toán',
          icon: 'shopping-cart',
        },
        draggable: true,
      },
      {
        id: 'task3',
        type: 'cardNode',
        position: { x: 400, y: 350 },
        data: {
          title: 'Chương trình khách hàng thân thiết',
          description: 'Triển khai hệ thống tích điểm và ưu đãi',
          icon: 'award',
        },
        draggable: true,
      },
    ],
    edges: [
      {
        id: 'e1',
        source: 'objective',
        target: 'kr1',
        type: 'customEdge',
        data: { animated: true },
      },
      {
        id: 'e2',
        source: 'objective',
        target: 'kr2',
        type: 'customEdge',
        data: { animated: true },
      },
      {
        id: 'e3',
        source: 'objective',
        target: 'kr3',
        type: 'customEdge',
        data: { animated: true },
      },
      {
        id: 'e4',
        source: 'kr1',
        target: 'task1',
        type: 'stepEdge',
      },
      {
        id: 'e5',
        source: 'kr2',
        target: 'task2',
        type: 'stepEdge',
      },
      {
        id: 'e6',
        source: 'kr3',
        target: 'task3',
        type: 'stepEdge',
      },
    ],
  },
};

/**
 * Trang hiển thị các loại node và edge trong React Flow
 */
const FlowNodesPage: React.FC = () => {
  // const { t } = useTranslation(); // Không sử dụng vì đã thay thế bằng text trực tiếp
  const [selectedExample, setSelectedExample] = useState<string>('basic');

  // Lấy nodes và edges dựa trên ví dụ được chọn
  const { nodes, edges } = useMemo(() => {
    return {
      nodes: flowExamples[selectedExample as keyof typeof flowExamples].nodes,
      edges: flowExamples[selectedExample as keyof typeof flowExamples].edges,
    };
  }, [selectedExample]);

  return (
    <Container>
      <Typography variant="h1" className="mb-6">
        Flow Nodes & Edges
      </Typography>

      <Typography className="mb-6">
        Các loại node và edge tùy chỉnh cho React Flow, hỗ trợ xây dựng các biểu đồ, sơ đồ tổ chức
        và quy trình.
      </Typography>

      <Card className="mb-8">
        <div className="p-6">
          <Typography variant="h2" className="mb-4">
            Các ví dụ
          </Typography>

          <div className="mb-4">
            <Select
              value={selectedExample}
              onChange={value => setSelectedExample(value as string)}
              options={Object.entries(flowExamples).map(([key, example]) => ({
                value: key,
                label: example.name,
              }))}
              placeholder="Chọn ví dụ"
              className="w-[200px]"
            />
          </div>

          <div className="w-full h-[600px] border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
            <ReactFlowProvider>
              <ReactFlow
                nodes={nodes}
                edges={edges}
                nodeTypes={nodeTypes}
                edgeTypes={edgeTypes}
                fitView
                attributionPosition="bottom-right"
                connectionLineType={ConnectionLineType.SmoothStep}
                nodesDraggable={true}
                nodesConnectable={true}
                elementsSelectable={true}
                defaultEdgeOptions={{ animated: true }}
                proOptions={{ hideAttribution: true }}
                onNodeDragStop={(_, node) => console.log('Node dragged:', node)}
              >
                <Background />
                <Controls />
                <MiniMap />
              </ReactFlow>
            </ReactFlowProvider>
          </div>
        </div>
      </Card>

      <Card className="mb-8">
        <div className="p-6">
          <Typography variant="h2" className="mb-4">
            Các loại Node
          </Typography>

          <Tabs
            items={[
              {
                key: 'card',
                label: 'Card Node',
                children: (
                  <>
                    <div
                      style={{
                        width: '100%',
                        height: '300px',
                        border: '1px solid #e2e8f0',
                        borderRadius: '0.5rem',
                        marginBottom: '1rem',
                        backgroundColor: '#f8fafc',
                      }}
                    >
                      <ReactFlowProvider>
                        <ReactFlow
                          nodes={[
                            {
                              id: 'card-1',
                              type: 'cardNode',
                              position: { x: 150, y: 100 },
                              data: {
                                title: 'Card Node',
                                description: 'Mô tả cho card node',
                                icon: 'file-text',
                                extraData: {
                                  'Trạng thái': 'Hoạt động',
                                  'Ngày tạo': '01/01/2023',
                                },
                              },
                              draggable: true,
                            },
                          ]}
                          edges={[]}
                          nodeTypes={nodeTypes}
                          fitView
                          nodesDraggable={true}
                          nodesConnectable={false}
                          elementsSelectable={true}
                          proOptions={{ hideAttribution: true }}
                        >
                          <Background />
                          <Controls />
                        </ReactFlow>
                      </ReactFlowProvider>
                    </div>
                    <ComponentDemo
                      title="Card Node"
                      description="Node hiển thị thông tin dạng card với tiêu đề, mô tả và dữ liệu bổ sung."
                      code={`
import { CardNode } from '@/shared/flow';

// Định nghĩa node types
const nodeTypes = {
  cardNode: CardNode
};

// Sử dụng trong nodes
const nodes = [
  {
    id: 'card-1',
    type: 'cardNode',
    position: { x: 100, y: 100 },
    data: {
      title: 'Card Node',
      description: 'Mô tả cho card node',
      icon: 'file-text',
      extraData: {
        'Trạng thái': 'Hoạt động',
        'Ngày tạo': '01/01/2023',
      },
    },
  }
];
                    `}
                    >
                      <div className="text-center">
                        <p>Xem demo ở trên</p>
                      </div>
                    </ComponentDemo>
                  </>
                ),
              },
              {
                key: 'process',
                label: 'Process Node',
                children: (
                  <>
                    <div
                      style={{
                        width: '100%',
                        height: '300px',
                        border: '1px solid #e2e8f0',
                        borderRadius: '0.5rem',
                        marginBottom: '1rem',
                        backgroundColor: '#f8fafc',
                      }}
                    >
                      <ReactFlowProvider>
                        <ReactFlow
                          nodes={[
                            {
                              id: 'process-1',
                              type: 'processNode',
                              position: { x: 150, y: 100 },
                              data: {
                                title: 'Process Node',
                                description: 'Đang xử lý...',
                                status: 'processing',
                              },
                              draggable: true,
                            },
                          ]}
                          edges={[]}
                          nodeTypes={nodeTypes}
                          fitView
                          nodesDraggable={true}
                          nodesConnectable={false}
                          elementsSelectable={true}
                          proOptions={{ hideAttribution: true }}
                        >
                          <Background />
                          <Controls />
                        </ReactFlow>
                      </ReactFlowProvider>
                    </div>
                    <ComponentDemo
                      title="Process Node"
                      description="Node hiển thị quy trình xử lý với các trạng thái khác nhau."
                      code={`
import { ProcessNode } from '@/shared/flow';

// Định nghĩa node types
const nodeTypes = {
  processNode: ProcessNode
};

// Sử dụng trong nodes
const nodes = [
  {
    id: 'process-1',
    type: 'processNode',
    position: { x: 100, y: 100 },
    data: {
      title: 'Process Node',
      description: 'Đang xử lý...',
      status: 'processing', // 'pending', 'processing', 'completed', 'error'
    },
  }
];
                    `}
                    >
                      <div className="text-center">
                        <p>Xem demo ở trên</p>
                      </div>
                    </ComponentDemo>
                  </>
                ),
              },
              {
                key: 'decision',
                label: 'Decision Node',
                children: (
                  <>
                    <div
                      style={{
                        width: '100%',
                        height: '300px',
                        border: '1px solid #e2e8f0',
                        borderRadius: '0.5rem',
                        marginBottom: '1rem',
                        backgroundColor: '#f8fafc',
                      }}
                    >
                      <ReactFlowProvider>
                        <ReactFlow
                          nodes={[
                            {
                              id: 'decision-1',
                              type: 'decisionNode',
                              position: { x: 150, y: 100 },
                              data: {
                                question: 'Có tiếp tục không?',
                                description: 'Quyết định tiếp theo',
                                yesLabel: 'Có',
                                noLabel: 'Không',
                              },
                              draggable: true,
                            },
                          ]}
                          edges={[]}
                          nodeTypes={nodeTypes}
                          fitView
                          nodesDraggable={true}
                          nodesConnectable={false}
                          elementsSelectable={true}
                          proOptions={{ hideAttribution: true }}
                        >
                          <Background />
                          <Controls />
                        </ReactFlow>
                      </ReactFlowProvider>
                    </div>
                    <ComponentDemo
                      title="Decision Node"
                      description="Node hiển thị quyết định (decision) trong flowchart với hai nhánh Yes/No."
                      code={`
import { DecisionNode } from '@/shared/flow';

// Định nghĩa node types
const nodeTypes = {
  decisionNode: DecisionNode
};

// Sử dụng trong nodes
const nodes = [
  {
    id: 'decision-1',
    type: 'decisionNode',
    position: { x: 100, y: 100 },
    data: {
      question: 'Có tiếp tục không?',
      description: 'Quyết định tiếp theo',
      yesLabel: 'Có',
      noLabel: 'Không',
    },
  }
];
                    `}
                    >
                      <div className="text-center">
                        <p>Xem demo ở trên</p>
                      </div>
                    </ComponentDemo>
                  </>
                ),
              },
              {
                key: 'startend',
                label: 'Start/End Node',
                children: (
                  <>
                    <div
                      style={{
                        width: '100%',
                        height: '300px',
                        border: '1px solid #e2e8f0',
                        borderRadius: '0.5rem',
                        marginBottom: '1rem',
                        backgroundColor: '#f8fafc',
                      }}
                    >
                      <ReactFlowProvider>
                        <ReactFlow
                          nodes={[
                            {
                              id: 'start-1',
                              type: 'startEndNode',
                              position: { x: 100, y: 100 },
                              data: {
                                type: 'start',
                                label: 'Bắt đầu',
                              },
                              draggable: true,
                            },
                            {
                              id: 'end-1',
                              type: 'startEndNode',
                              position: { x: 300, y: 100 },
                              data: {
                                type: 'end',
                                label: 'Kết thúc',
                              },
                              draggable: true,
                            },
                          ]}
                          edges={[]}
                          nodeTypes={nodeTypes}
                          fitView
                          nodesDraggable={true}
                          nodesConnectable={false}
                          elementsSelectable={true}
                          proOptions={{ hideAttribution: true }}
                        >
                          <Background />
                          <Controls />
                        </ReactFlow>
                      </ReactFlowProvider>
                    </div>
                    <ComponentDemo
                      title="Start/End Node"
                      description="Node hiển thị điểm bắt đầu hoặc kết thúc trong flowchart."
                      code={`
import { StartEndNode } from '@/shared/flow';

// Định nghĩa node types
const nodeTypes = {
  startEndNode: StartEndNode
};

// Sử dụng trong nodes
const nodes = [
  {
    id: 'start-1',
    type: 'startEndNode',
    position: { x: 100, y: 100 },
    data: {
      type: 'start', // 'start' hoặc 'end'
      label: 'Bắt đầu',
    },
  }
];
                    `}
                    >
                      <div className="text-center">
                        <p>Xem demo ở trên</p>
                      </div>
                    </ComponentDemo>
                  </>
                ),
              },
            ]}
            defaultActiveKey="card"
            type="underline"
          />
        </div>
      </Card>

      <Card className="mb-8">
        <div className="p-6">
          <Typography variant="h2" className="mb-4">
            Các loại Edge
          </Typography>

          <Tabs
            items={[
              {
                key: 'custom',
                label: 'Custom Edge',
                children: (
                  <>
                    <div
                      style={{
                        width: '100%',
                        height: '300px',
                        border: '1px solid #e2e8f0',
                        borderRadius: '0.5rem',
                        marginBottom: '1rem',
                        backgroundColor: '#f8fafc',
                      }}
                    >
                      <ReactFlowProvider>
                        <ReactFlow
                          nodes={[
                            {
                              id: 'node-1',
                              type: 'cardNode',
                              position: { x: 50, y: 100 },
                              data: { title: 'Node 1', description: 'Source node' },
                              draggable: true,
                            },
                            {
                              id: 'node-2',
                              type: 'cardNode',
                              position: { x: 350, y: 100 },
                              data: { title: 'Node 2', description: 'Target node' },
                              draggable: true,
                            },
                          ]}
                          edges={[
                            {
                              id: 'edge-demo',
                              source: 'node-1',
                              target: 'node-2',
                              type: 'customEdge',
                              data: {
                                label: 'Custom Edge',
                                color: '#3b82f6',
                                strokeWidth: 2,
                                animated: true,
                                strokeType: 'dashed',
                              },
                            },
                          ]}
                          nodeTypes={nodeTypes}
                          edgeTypes={edgeTypes}
                          fitView
                          nodesDraggable={true}
                          nodesConnectable={false}
                          elementsSelectable={true}
                          proOptions={{ hideAttribution: true }}
                        >
                          <Background />
                          <Controls />
                        </ReactFlow>
                      </ReactFlowProvider>
                    </div>
                    <ComponentDemo
                      title="Custom Edge"
                      description="Edge tùy chỉnh với nhiều thuộc tính như màu sắc, độ dày, kiểu đường và animation."
                      code={`
import { CustomEdge } from '@/shared/flow';

// Định nghĩa edge types
const edgeTypes = {
  customEdge: CustomEdge
};

// Sử dụng trong edges
const edges = [
  {
    id: 'edge-1',
    source: 'node-1',
    target: 'node-2',
    type: 'customEdge',
    data: {
      label: 'Kết nối',
      color: '#3b82f6',
      strokeWidth: 2,
      animated: true,
      strokeType: 'dashed', // 'solid', 'dashed', 'dotted'
    },
  }
];
                    `}
                    >
                      <div className="text-center">
                        <p>Xem demo ở trên</p>
                      </div>
                    </ComponentDemo>
                  </>
                ),
              },
              {
                key: 'step',
                label: 'Step Edge',
                children: (
                  <>
                    <div
                      style={{
                        width: '100%',
                        height: '300px',
                        border: '1px solid #e2e8f0',
                        borderRadius: '0.5rem',
                        marginBottom: '1rem',
                        backgroundColor: '#f8fafc',
                      }}
                    >
                      <ReactFlowProvider>
                        <ReactFlow
                          nodes={[
                            {
                              id: 'node-1',
                              type: 'processNode',
                              position: { x: 50, y: 50 },
                              data: { title: 'Process 1', status: 'completed' },
                              draggable: true,
                            },
                            {
                              id: 'node-2',
                              type: 'processNode',
                              position: { x: 350, y: 150 },
                              data: { title: 'Process 2', status: 'processing' },
                              draggable: true,
                            },
                          ]}
                          edges={[
                            {
                              id: 'edge-demo',
                              source: 'node-1',
                              target: 'node-2',
                              type: 'stepEdge',
                              data: {
                                label: 'Step Edge',
                                color: '#3b82f6',
                                strokeWidth: 2,
                                animated: true,
                              },
                            },
                          ]}
                          nodeTypes={nodeTypes}
                          edgeTypes={edgeTypes}
                          fitView
                          nodesDraggable={true}
                          nodesConnectable={false}
                          elementsSelectable={true}
                          proOptions={{ hideAttribution: true }}
                        >
                          <Background />
                          <Controls />
                        </ReactFlow>
                      </ReactFlowProvider>
                    </div>
                    <ComponentDemo
                      title="Step Edge"
                      description="Edge kiểu bậc thang với góc vuông, phù hợp cho các flowchart."
                      code={`
import { StepEdge } from '@/shared/flow';

// Định nghĩa edge types
const edgeTypes = {
  stepEdge: StepEdge
};

// Sử dụng trong edges
const edges = [
  {
    id: 'edge-1',
    source: 'node-1',
    target: 'node-2',
    type: 'stepEdge',
    data: {
      label: 'Kết nối',
      color: '#3b82f6',
      strokeWidth: 2,
      animated: true,
      strokeType: 'dashed', // 'solid', 'dashed', 'dotted'
    },
  }
];
                    `}
                    >
                      <div className="text-center">
                        <p>Xem demo ở trên</p>
                      </div>
                    </ComponentDemo>
                  </>
                ),
              },
            ]}
            defaultActiveKey="custom"
            type="underline"
          />
        </div>
      </Card>
    </Container>
  );
};

export default FlowNodesPage;
