import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Input, Icon } from '@/shared/components/common';

import { ComponentDemo } from '../components';

const InputsPage: React.FC = () => {
  const { t } = useTranslation();
  const [textValue, setTextValue] = useState('');
  const [passwordValue, setPasswordValue] = useState('');

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.categories.inputs.title')}
        </h1>
        <p className="text-muted">{t('components.categories.inputs.description')}</p>
      </div>

      <ComponentDemo
        title={t('components.inputs.text.title')}
        description={t('components.inputs.text.description')}
        code={`import { Input } from '@/shared/components/common';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();
const [value, setValue] = useState('');

<Input
  type="text"
  label={t('components.inputs.labels.username')}
  placeholder={t('components.inputs.placeholders.username')}
  value={value}
  onChange={(e) => setValue(e.target.value)}
/>`}
      >
        <div className="w-full max-w-md">
          <Input
            type="text"
            label={t('components.inputs.labels.username')}
            placeholder={t('components.inputs.placeholders.username')}
            value={textValue}
            onChange={e => setTextValue(e.target.value)}
          />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.inputs.password.title')}
        description={t('components.inputs.password.description')}
        code={`import { Input } from '@/shared/components/common';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();
const [value, setValue] = useState('');

<Input
  type="password"
  label={t('components.inputs.labels.password')}
  placeholder={t('components.inputs.placeholders.password')}
  value={value}
  onChange={(e) => setValue(e.target.value)}
/>`}
      >
        <div className="w-full max-w-md">
          <Input
            type="password"
            label={t('components.inputs.labels.password')}
            placeholder={t('components.inputs.placeholders.password')}
            value={passwordValue}
            onChange={e => setPasswordValue(e.target.value)}
          />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.inputs.helperText.title')}
        description={t('components.inputs.helperText.description')}
        code={`import { Input } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();

<Input
  type="email"
  label={t('components.inputs.labels.email')}
  placeholder={t('components.inputs.placeholders.email')}
  helperText={t('components.inputs.helpers.emailPrivacy')}
/>`}
      >
        <div className="w-full max-w-md">
          <Input
            type="email"
            label={t('components.inputs.labels.email')}
            placeholder={t('components.inputs.placeholders.email')}
            helperText={t('components.inputs.helpers.emailPrivacy')}
          />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.inputs.error.title')}
        description={t('components.inputs.error.description')}
        code={`import { Input } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();

<Input
  type="email"
  label={t('components.inputs.labels.email')}
  placeholder={t('components.inputs.placeholders.email')}
  error={t('components.inputs.errors.invalidEmail')}
/>`}
      >
        <div className="w-full max-w-md">
          <Input
            type="email"
            label={t('components.inputs.labels.email')}
            placeholder={t('components.inputs.placeholders.email')}
            error={t('components.inputs.errors.invalidEmail')}
          />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.inputs.disabled.title')}
        description={t('components.inputs.disabled.description')}
        code={`import { Input } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();

<Input
  type="text"
  label={t('components.inputs.labels.username')}
  placeholder={t('components.inputs.placeholders.username')}
  disabled
  value="johndoe"
/>`}
      >
        <div className="w-full max-w-md">
          <Input
            type="text"
            label={t('components.inputs.labels.username')}
            placeholder={t('components.inputs.placeholders.username')}
            disabled
            value="johndoe"
          />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.inputs.withIcon.title')}
        description={t('components.inputs.withIcon.description')}
        code={`import { Input, Icon } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();

<Input
  type="search"
  label={t('components.inputs.labels.search')}
  placeholder={t('components.inputs.placeholders.search')}
  leftIcon={<Icon name="search" />}
/>

<Input
  type="text"
  label={t('components.inputs.labels.website')}
  placeholder={t('components.inputs.placeholders.website')}
  rightIcon={<Icon name="link" />}
/>`}
      >
        <div className="w-full max-w-md space-y-4">
          <Input
            type="search"
            label={t('components.inputs.labels.search')}
            placeholder={t('components.inputs.placeholders.search')}
            leftIcon={<Icon name="search" />}
          />
          <Input
            type="text"
            label={t('components.inputs.labels.website')}
            placeholder={t('components.inputs.placeholders.website')}
            rightIcon={<Icon name="link" />}
          />
        </div>
      </ComponentDemo>
    </div>
  );
};

export default InputsPage;
