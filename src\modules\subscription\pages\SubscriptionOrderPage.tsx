/**
 * Trang đặt hàng gói dịch vụ
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';

import {
  Typography,
  Container,
  Button,
  Card,
  Image,
  Icon,
  ResponsiveGrid,
} from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';

import { PaymentMethod } from '../components';
import { ServiceType, SubscriptionDuration } from '../types';

/**
 * Thông tin gói dịch vụ đã chọn
 */
interface SelectedPackageInfo {
  id: string;
  name: string;
  type: ServiceType;
  price: number;
  duration: SubscriptionDuration;
}

/**
 * Trang đặt hàng gói dịch vụ
 */
const SubscriptionOrderPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { state } = location;

  // Sử dụng hook theme
  useTheme();

  // L<PERSON>y thông tin gói dịch vụ từ state
  const packageInfo: SelectedPackageInfo = state?.packageInfo || {
    id: '',
    name: '',
    type: ServiceType.MAIN,
    price: 0,
    duration: SubscriptionDuration.MONTHLY,
  };

  // Phương thức thanh toán mặc định là R-Point
  const paymentMethod = PaymentMethod.R_POINT;

  // Giả lập giảm giá 10%
  const discount = Math.round(packageInfo.price * 0.1);

  // Xử lý khi thanh toán
  const handleCheckout = () => {
    console.log('Checkout with:', {
      packageInfo,
      paymentMethod,
      discount,
    });

    // Chuyển hướng đến trang thanh toán thành công
    navigate('/subscription/payment-success', {
      state: {
        orderInfo: {
          packageName: packageInfo.name,
          packageType: packageInfo.type,
          duration: packageInfo.duration,
          price: packageInfo.price,
          discount,
          total: packageInfo.price - discount,
          paymentMethod,
          orderNumber: `ORD-${Date.now()}`,
          paymentDate: new Date().toISOString(),
        },
      },
    });
  };

  // Xử lý khi quay lại
  const handleBack = () => {
    navigate('/subscription/packages');
  };

  return (
    <Container className="py-8">
      {/* Tiêu đề trang */}
      <div className="mb-8">
        <Typography variant="h3" className="font-bold mb-2 text-center">
          {t('subscription.order.title', 'Xác nhận đơn hàng')}
        </Typography>
      </div>

      <div className="max-w-5xl mx-auto">
        <ResponsiveGrid
          gap={6}
          maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}
          maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2 }}
        >
          {/* Cột trái: Thông tin chi tiết gói */}
          <Card>
            <Typography variant="h5" className="font-bold mb-6 flex items-center">
              <Icon name="package" size="md" className="mr-2 text-primary" />
              {t('subscription.order.packageDetails', 'Thông tin gói dịch vụ')}
            </Typography>

            {/* Thông tin gói */}
            <div className="space-y-5">
              {/* Tên gói */}
              <div className="flex justify-between items-center pb-3 border-b border-gray-100 dark:border-gray-800">
                <Typography variant="body2" className="text-muted">
                  {t('subscription.order.packageName', 'Tên gói')}
                </Typography>
                <Typography variant="body1" className="font-semibold">
                  {packageInfo.name}
                </Typography>
              </div>

              {/* Loại gói */}
              <div className="flex justify-between items-center pb-3 border-b border-gray-100 dark:border-gray-800">
                <Typography variant="body2" className="text-muted">
                  {t('subscription.order.packageType', 'Loại gói')}
                </Typography>
                <Typography variant="body1" className="font-semibold">
                  {packageInfo.type === ServiceType.MAIN
                    ? t('subscription.types.main', 'Gói chính')
                    : t('subscription.types.feature', 'Tính năng')}
                </Typography>
              </div>

              {/* Thời hạn */}
              <div className="flex justify-between items-center pb-3 border-b border-gray-100 dark:border-gray-800">
                <Typography variant="body2" className="text-muted">
                  {t('subscription.order.duration', 'Thời hạn')}
                </Typography>
                <Typography variant="body1" className="font-semibold">
                  {packageInfo.duration === SubscriptionDuration.MONTHLY
                    ? t('subscription.duration.monthly', 'Hàng tháng')
                    : packageInfo.duration === SubscriptionDuration.SEMI_ANNUAL
                      ? t('subscription.duration.semiAnnual', '6 tháng')
                      : t('subscription.duration.annual', 'Hàng năm')}
                </Typography>
              </div>

              {/* Thông tin thanh toán R-Point */}
              <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center mb-4">
                  <Image
                    src="/src/shared/assets/images/rpoint.png"
                    alt="R-Point"
                    width={28}
                    height={28}
                    className="mr-3"
                  />
                  <Typography variant="subtitle1" className="font-semibold">
                    {t('subscription.order.rPointPayment', 'Thanh toán bằng R-Point')}
                  </Typography>
                </div>

                <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
                  <div className="flex justify-between mb-2">
                    <Typography variant="body2" className="text-muted">
                      {t('subscription.order.availableRPoints', 'R-Point khả dụng')}:
                    </Typography>
                    <div className="flex items-center">
                      <Typography variant="body1" className="font-semibold mr-1">
                        10,000
                      </Typography>
                      <Image
                        src="/src/shared/assets/images/rpoint.png"
                        alt="R-Point"
                        width={16}
                        height={16}
                      />
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <Typography variant="body2" className="text-muted">
                      {t('subscription.order.requiredRPoints', 'R-Point cần thiết')}:
                    </Typography>
                    <div className="flex items-center">
                      <Typography variant="body1" className="font-semibold mr-1">
                        {Math.round(packageInfo.price / 1000)}
                      </Typography>
                      <Image
                        src="/src/shared/assets/images/rpoint.png"
                        alt="R-Point"
                        width={16}
                        height={16}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Cột phải: Thông tin đơn hàng và giá tiền */}
          <Card>
            <Typography variant="h5" className="font-bold mb-6 flex items-center">
              <Icon name="credit-card" size="md" className="mr-2 text-primary" />
              {t('subscription.order.orderSummary', 'Thông tin đơn hàng')}
            </Typography>

            <div className="space-y-4">
              {/* Giá gói */}
              <div className="flex justify-between pb-3">
                <Typography variant="body2" className="text-muted">
                  {t('subscription.order.packagePrice', 'Giá gói')}
                </Typography>
                <div className="flex items-center">
                  <Typography variant="body1" className="font-semibold mr-1">
                    {Math.round(packageInfo.price / 1000)}
                  </Typography>
                  <Image
                    src="/src/shared/assets/images/rpoint.png"
                    alt="R-Point"
                    width={16}
                    height={16}
                  />
                </div>
              </div>

              {/* Giảm giá */}
              <div className="flex justify-between pb-3 border-b border-gray-200 dark:border-gray-700">
                <Typography variant="body2" className="text-muted">
                  {t('subscription.order.discount', 'Giảm giá')}
                </Typography>
                <div className="flex items-center">
                  <Typography
                    variant="body1"
                    className="font-semibold mr-1 text-green-600 dark:text-green-400"
                  >
                    -{Math.round(discount / 1000)}
                  </Typography>
                  <Image
                    src="/src/shared/assets/images/rpoint.png"
                    alt="R-Point"
                    width={16}
                    height={16}
                  />
                </div>
              </div>

              {/* Tổng cộng */}
              <div className="flex justify-between pt-2">
                <Typography variant="subtitle1" className="font-bold">
                  {t('subscription.order.total', 'Tổng cộng')}
                </Typography>
                <div className="flex items-center">
                  <Typography variant="h4" className="font-bold text-primary mr-1">
                    {Math.round((packageInfo.price - discount) / 1000)}
                  </Typography>
                  <Image
                    src="/src/shared/assets/images/rpoint.png"
                    alt="R-Point"
                    width={20}
                    height={20}
                  />
                </div>
              </div>

              {/* Mô tả */}
              <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <Typography variant="body2" className="text-muted mb-6">
                  {t(
                    'subscription.order.rPointDescription',
                    'Thanh toán sẽ được thực hiện ngay lập tức khi bạn nhấn nút "Thanh toán" bên dưới.'
                  )}
                </Typography>

                {/* Nút điều hướng */}
                <div className="flex flex-col space-y-3 mt-4">
                  <Button variant="primary" onClick={handleCheckout} size="lg">
                    <div className="flex items-center justify-center">
                      <Image
                        src="/src/shared/assets/images/rpoint.png"
                        alt="R-Point"
                        width={20}
                        height={20}
                        className="mr-2"
                      />
                      {t('subscription.order.payWithRPoint', 'Thanh toán bằng R-Point')}
                    </div>
                  </Button>
                  <Button variant="outline" onClick={handleBack}>
                    {t('subscription.order.back', 'Quay lại')}
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </ResponsiveGrid>
      </div>
    </Container>
  );
};

export default SubscriptionOrderPage;
