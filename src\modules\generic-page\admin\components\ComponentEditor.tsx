import React from 'react';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Typography,
  Input,
  Textarea,
  Select,
  Checkbox,
  Button,
  Tabs,
  FormGrid,
  FormItem,
  Toggle,
} from '@/shared/components/common';

import { FieldConfig } from '../../types/generic-page.types';

interface ComponentEditorProps {
  component: FieldConfig;
  onUpdate: (updates: Partial<FieldConfig>) => void;
  onDelete: () => void;
}

/**
 * Component editor for editing component properties
 */
const ComponentEditor: React.FC<ComponentEditorProps> = ({ component, onUpdate, onDelete }) => {
  const { t } = useTranslation();

  // Update component config
  const updateConfig = (key: string, value: unknown): void => {
    onUpdate({
      config: {
        ...component.config,
        [key]: value,
      },
    });
  };

  // Update grid config
  const updateGrid = (key: string, value: unknown): void => {
    onUpdate({
      grid: {
        ...component.grid,
        [key]: value,
      },
    });
  };

  return (
    <Card className="w-full">
      <div className="p-4 border-b flex justify-between items-center">
        <Typography variant="h3">
          {t('genericPage.componentEditor.title', 'Component Properties')}
        </Typography>
        <Button
          variant="ghost"
          size="sm"
          onClick={onDelete}
          className="text-red-500 hover:text-red-700"
        >
          {t('genericPage.componentEditor.delete', 'Delete')}
        </Button>
      </div>

      <Tabs
        items={[
          {
            key: 'basic',
            label: t('genericPage.componentEditor.basicTab', 'Basic'),
            children: (
              <FormGrid columns={1} gap="md">
                <FormItem label={t('genericPage.componentEditor.label', 'Label')}>
                  <Input
                    value={component.config.label || ''}
                    onChange={e => updateConfig('label', e.target.value)}
                    placeholder={t(
                      'genericPage.componentEditor.labelPlaceholder',
                      'Component Label'
                    )}
                  />
                </FormItem>

                <FormItem label={t('genericPage.componentEditor.id', 'ID')}>
                  <Input
                    value={component.config.id}
                    onChange={e => updateConfig('id', e.target.value)}
                    placeholder={t('genericPage.componentEditor.idPlaceholder', 'Component ID')}
                  />
                </FormItem>

                {component.component === 'text-input' && (
                  <FormItem label={t('genericPage.componentEditor.type', 'Input Type')}>
                    <Select
                      value={component.config.type || 'text'}
                      onChange={value => updateConfig('type', value)}
                      options={[
                        { label: 'Text', value: 'text' },
                        { label: 'Email', value: 'email' },
                        { label: 'Password', value: 'password' },
                        { label: 'Number', value: 'number' },
                        { label: 'Tel', value: 'tel' },
                        { label: 'URL', value: 'url' },
                      ]}
                    />
                  </FormItem>
                )}

                <FormItem label={t('genericPage.componentEditor.placeholder', 'Placeholder')}>
                  <Input
                    value={component.config.placeholder || ''}
                    onChange={e => updateConfig('placeholder', e.target.value)}
                    placeholder={t(
                      'genericPage.componentEditor.placeholderPlaceholder',
                      'Placeholder text'
                    )}
                  />
                </FormItem>

                <FormItem label={t('genericPage.componentEditor.helperText', 'Helper Text')}>
                  <Input
                    value={component.config.helperText || ''}
                    onChange={e => updateConfig('helperText', e.target.value)}
                    placeholder={t(
                      'genericPage.componentEditor.helperTextPlaceholder',
                      'Helper text'
                    )}
                  />
                </FormItem>

                <FormItem label={t('genericPage.componentEditor.defaultValue', 'Default Value')}>
                  <Input
                    value={
                      component.config.defaultValue ? String(component.config.defaultValue) : ''
                    }
                    onChange={e => updateConfig('defaultValue', e.target.value)}
                    placeholder={t(
                      'genericPage.componentEditor.defaultValuePlaceholder',
                      'Default value'
                    )}
                  />
                </FormItem>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="required"
                    checked={component.config.required || false}
                    onChange={checked => updateConfig('required', checked)}
                  />
                  <label htmlFor="required" className="text-sm">
                    {t('genericPage.componentEditor.required', 'Required')}
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="disabled"
                    checked={component.config.disabled || false}
                    onChange={checked => updateConfig('disabled', checked)}
                  />
                  <label htmlFor="disabled" className="text-sm">
                    {t('genericPage.componentEditor.disabled', 'Disabled')}
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="readOnly"
                    checked={component.config.readOnly || false}
                    onChange={checked => updateConfig('readOnly', checked)}
                  />
                  <label htmlFor="readOnly" className="text-sm">
                    {t('genericPage.componentEditor.readOnly', 'Read Only')}
                  </label>
                </div>
              </FormGrid>
            ),
          },
          {
            key: 'layout',
            label: t('genericPage.componentEditor.layoutTab', 'Layout'),
            children: (
              <FormGrid columns={2} gap="md">
                <FormItem label={t('genericPage.componentEditor.x', 'X Position')}>
                  <Input
                    type="number"
                    value={component.grid.x}
                    onChange={e => updateGrid('x', parseInt(e.target.value) || 0)}
                    min={0}
                    max={11}
                  />
                </FormItem>

                <FormItem label={t('genericPage.componentEditor.y', 'Y Position')}>
                  <Input
                    type="number"
                    value={component.grid.y}
                    onChange={e => updateGrid('y', parseInt(e.target.value) || 0)}
                    min={0}
                  />
                </FormItem>

                <FormItem label={t('genericPage.componentEditor.width', 'Width')}>
                  <Input
                    type="number"
                    value={component.grid.w}
                    onChange={e => updateGrid('w', parseInt(e.target.value) || 1)}
                    min={1}
                    max={12}
                  />
                </FormItem>

                <FormItem label={t('genericPage.componentEditor.height', 'Height')}>
                  <Input
                    type="number"
                    value={component.grid.h}
                    onChange={e => updateGrid('h', parseInt(e.target.value) || 1)}
                    min={1}
                  />
                </FormItem>

                <FormItem label={t('genericPage.componentEditor.minWidth', 'Min Width')}>
                  <Input
                    type="number"
                    value={component.grid.minW || 1}
                    onChange={e => updateGrid('minW', parseInt(e.target.value) || 1)}
                    min={1}
                    max={12}
                  />
                </FormItem>

                <FormItem label={t('genericPage.componentEditor.minHeight', 'Min Height')}>
                  <Input
                    type="number"
                    value={component.grid.minH || 1}
                    onChange={e => updateGrid('minH', parseInt(e.target.value) || 1)}
                    min={1}
                  />
                </FormItem>

                <div className="col-span-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">
                      {t('genericPage.componentEditor.static', 'Static (Non-draggable)')}
                    </label>
                    <Toggle
                      checked={component.grid.static || false}
                      onChange={checked => updateGrid('static', checked)}
                    />
                  </div>
                </div>

                <div className="col-span-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">
                      {t('genericPage.componentEditor.draggable', 'Draggable')}
                    </label>
                    <Toggle
                      checked={component.grid.isDraggable !== false}
                      onChange={checked => updateGrid('isDraggable', checked)}
                      disabled={component.grid.static || false}
                    />
                  </div>
                </div>

                <div className="col-span-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">
                      {t('genericPage.componentEditor.resizable', 'Resizable')}
                    </label>
                    <Toggle
                      checked={component.grid.isResizable !== false}
                      onChange={checked => updateGrid('isResizable', checked)}
                      disabled={component.grid.static || false}
                    />
                  </div>
                </div>
              </FormGrid>
            ),
          },
          {
            key: 'validation',
            label: t('genericPage.componentEditor.validationTab', 'Validation'),
            children: (
              <FormGrid columns={1} gap="md">
                {(component.component === 'text-input' || component.component === 'text-area') && (
                  <>
                    <FormItem label={t('genericPage.componentEditor.minLength', 'Min Length')}>
                      <Input
                        type="number"
                        value={component.config.validation?.minLength || ''}
                        onChange={e =>
                          onUpdate({
                            config: {
                              ...component.config,
                              validation: {
                                ...component.config.validation,
                                minLength: e.target.value ? parseInt(e.target.value) : undefined,
                              },
                            },
                          })
                        }
                        min={0}
                      />
                    </FormItem>

                    <FormItem label={t('genericPage.componentEditor.maxLength', 'Max Length')}>
                      <Input
                        type="number"
                        value={component.config.validation?.maxLength || ''}
                        onChange={e =>
                          onUpdate({
                            config: {
                              ...component.config,
                              validation: {
                                ...component.config.validation,
                                maxLength: e.target.value ? parseInt(e.target.value) : undefined,
                              },
                            },
                          })
                        }
                        min={0}
                      />
                    </FormItem>

                    <FormItem label={t('genericPage.componentEditor.pattern', 'Regex Pattern')}>
                      <Input
                        value={component.config.validation?.pattern || ''}
                        onChange={e =>
                          onUpdate({
                            config: {
                              ...component.config,
                              validation: {
                                ...component.config.validation,
                                pattern: e.target.value || undefined,
                              },
                            },
                          })
                        }
                        placeholder={t(
                          'genericPage.componentEditor.patternPlaceholder',
                          'Regular expression pattern'
                        )}
                      />
                    </FormItem>
                  </>
                )}

                {component.component === 'number-input' && (
                  <>
                    <FormItem label={t('genericPage.componentEditor.min', 'Min Value')}>
                      <Input
                        type="number"
                        value={component.config.min || ''}
                        onChange={e =>
                          updateConfig('min', e.target.value ? parseInt(e.target.value) : undefined)
                        }
                      />
                    </FormItem>

                    <FormItem label={t('genericPage.componentEditor.max', 'Max Value')}>
                      <Input
                        type="number"
                        value={component.config.max || ''}
                        onChange={e =>
                          updateConfig('max', e.target.value ? parseInt(e.target.value) : undefined)
                        }
                      />
                    </FormItem>

                    <FormItem label={t('genericPage.componentEditor.step', 'Step')}>
                      <Input
                        type="number"
                        value={component.config.step || ''}
                        onChange={e =>
                          updateConfig(
                            'step',
                            e.target.value ? parseInt(e.target.value) : undefined
                          )
                        }
                        min={0}
                      />
                    </FormItem>
                  </>
                )}
              </FormGrid>
            ),
          },
          {
            key: 'advanced',
            label: t('genericPage.componentEditor.advancedTab', 'Advanced'),
            children: (
              <FormGrid columns={1} gap="md">
                <FormItem label={t('genericPage.componentEditor.variant', 'Variant')}>
                  <Select
                    value={component.config.variant || 'default'}
                    onChange={value => updateConfig('variant', value)}
                    options={[
                      { label: 'Default', value: 'default' },
                      { label: 'Outline', value: 'outline' },
                      { label: 'Filled', value: 'filled' },
                    ]}
                  />
                </FormItem>

                <FormItem label={t('genericPage.componentEditor.size', 'Size')}>
                  <Select
                    value={component.config.size || 'md'}
                    onChange={value => updateConfig('size', value)}
                    options={[
                      { label: 'Small', value: 'sm' },
                      { label: 'Medium', value: 'md' },
                      { label: 'Large', value: 'lg' },
                    ]}
                  />
                </FormItem>

                {component.component === 'select-dropdown' && (
                  <FormItem label={t('genericPage.componentEditor.options', 'Options')}>
                    <Textarea
                      value={
                        Array.isArray(component.config.options)
                          ? component.config.options
                              .map(opt =>
                                typeof opt === 'string' ? opt : `${opt.label}:${opt.value}`
                              )
                              .join('\n')
                          : ''
                      }
                      onChange={e => {
                        const options = e.target.value
                          .split('\n')
                          .filter(Boolean)
                          .map(line => {
                            const [label, value] = line.split(':');
                            return value ? { label, value } : line;
                          });
                        updateConfig('options', options);
                      }}
                      placeholder={t(
                        'genericPage.componentEditor.optionsPlaceholder',
                        'One option per line\nFormat: label:value'
                      )}
                      rows={5}
                    />
                  </FormItem>
                )}

                <FormItem label={t('genericPage.componentEditor.customCss', 'Custom CSS Class')}>
                  <Input
                    value={component.config.className ? String(component.config.className) : ''}
                    onChange={e => updateConfig('className', e.target.value)}
                    placeholder={t(
                      'genericPage.componentEditor.customCssPlaceholder',
                      'CSS class names'
                    )}
                  />
                </FormItem>

                <FormItem label={t('genericPage.componentEditor.zIndex', 'Z-Index')}>
                  <Input
                    type="number"
                    value={component.config.zIndex ? String(component.config.zIndex) : ''}
                    onChange={e =>
                      updateConfig('zIndex', e.target.value ? parseInt(e.target.value) : undefined)
                    }
                    placeholder={t(
                      'genericPage.componentEditor.zIndexPlaceholder',
                      'Z-Index value'
                    )}
                    min={0}
                    max={1000}
                  />
                </FormItem>
              </FormGrid>
            ),
          },
        ]}
        defaultActiveKey="basic"
      />
    </Card>
  );
};

export default ComponentEditor;
