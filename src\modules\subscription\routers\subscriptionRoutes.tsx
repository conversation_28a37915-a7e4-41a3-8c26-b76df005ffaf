/**
 * Routes cho module Subscription
 */
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';

import i18n from '@/lib/i18n';
import { Loading } from '@/shared/components/common';
import MainLayout from '@/shared/layouts/MainLayout';

// Import Subscription pages
const SubscriptionPackagesPage = lazy(
  () => import('@/modules/subscription/pages/SubscriptionPackagesPage')
);
const SubscriptionOrderPage = lazy(
  () => import('@/modules/subscription/pages/SubscriptionOrderPage')
);
const SubscriptionPaymentSuccessPage = lazy(
  () => import('@/modules/subscription/pages/SubscriptionPaymentSuccessPage')
);

/**
 * Subscription module routes
 */
export const subscriptionRoutes: RouteObject[] = [
  {
    path: '/subscription/packages',
    element: (
      <MainLayout title={i18n.t('subscription:title')}>
        <Suspense fallback={<Loading />}>
          <SubscriptionPackagesPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/subscription/order',
    element: (
      <MainLayout title={i18n.t('subscription:order.title')}>
        <Suspense fallback={<Loading />}>
          <SubscriptionOrderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/subscription/payment-success',
    element: (
      <MainLayout title={i18n.t('subscription:payment.success')}>
        <Suspense fallback={<Loading />}>
          <SubscriptionPaymentSuccessPage />
        </Suspense>
      </MainLayout>
    ),
  },
  // Giữ lại route cũ để tương thích ngược
  {
    path: '/subscription',
    element: (
      <MainLayout title={i18n.t('subscription:title')}>
        <Suspense fallback={<Loading />}>
          <SubscriptionPackagesPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default subscriptionRoutes;
