/**
 * Trang hiển thị thông báo thanh toán thành công
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';

import {
  Typography,
  Container,
  Button,
  Card,
  Icon,
  Divider,
  Image,
  ResponsiveGrid,
} from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';

import { PaymentMethod } from '../components';
import { ServiceType, SubscriptionDuration } from '../types';
import { getDurationLabel } from '../utils';

/**
 * Thông tin đơn hàng
 */
interface OrderInfo {
  packageName: string;
  packageType: ServiceType;
  duration: SubscriptionDuration;
  price: number;
  discount: number;
  total: number;
  paymentMethod: PaymentMethod;
  orderNumber: string;
  paymentDate: string;
}

/**
 * Trang hiển thị thông báo thanh toán thành công
 */
const SubscriptionPaymentSuccessPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { state } = location;

  // Sử dụng hook theme
  useTheme();

  // Lấy thông tin đơn hàng từ state
  const orderInfo: OrderInfo = state?.orderInfo || {
    packageName: '',
    packageType: ServiceType.MAIN,
    duration: SubscriptionDuration.MONTHLY,
    price: 0,
    discount: 0,
    total: 0,
    paymentMethod: PaymentMethod.BANK_TRANSFER,
    orderNumber: '',
    paymentDate: new Date().toISOString(),
  };

  // Xử lý khi quay lại danh sách gói
  const handleBackToPackages = () => {
    navigate('/subscription/packages');
  };

  // Lấy tên phương thức thanh toán
  const getPaymentMethodName = (method: PaymentMethod): string => {
    switch (method) {
      case PaymentMethod.BANK_TRANSFER:
        return t('subscription.order.bankTransfer', 'Chuyển khoản ngân hàng');
      case PaymentMethod.CREDIT_CARD:
        return t('subscription.order.creditCard', 'Thẻ tín dụng');
      case PaymentMethod.E_WALLET:
        return t('subscription.order.eWallet', 'Ví điện tử');
      case PaymentMethod.R_POINT:
        return t('subscription.order.rPoint', 'R-Point');
      default:
        return '';
    }
  };

  return (
    <Container className="py-8">
      <ResponsiveGrid
        gap={6}
        maxColumns={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 1 }}
        className="max-w-2xl mx-auto"
      >
        <Card className="p-8">
          {/* Icon thành công */}
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 rounded-full bg-success/20 flex items-center justify-center">
              <Icon name="check" size="lg" className="text-success" />
            </div>
          </div>

          {/* Tiêu đề */}
          <div className="text-center mb-8">
            <Typography variant="h3" className="font-bold mb-2">
              {t('subscription.order.orderSuccess', 'Thanh toán thành công!')}
            </Typography>
            <Typography variant="body1" className="text-muted">
              {t(
                'subscription.order.orderSuccessMessage',
                'Cảm ơn bạn đã đăng ký dịch vụ. Gói dịch vụ của bạn đã được kích hoạt.'
              )}
            </Typography>
          </div>

          {/* Thông tin đơn hàng */}
          <div className="space-y-4 mb-8">
            <div className="flex justify-between">
              <Typography variant="body1">
                {t('subscription.order.orderNumber', 'Mã đơn hàng')}
              </Typography>
              <Typography variant="body1" className="font-medium">
                {orderInfo.orderNumber}
              </Typography>
            </div>

            <div className="flex justify-between">
              <Typography variant="body1">
                {t('subscription.order.paymentDate', 'Ngày thanh toán')}
              </Typography>
              <Typography variant="body1" className="font-medium">
                {new Date(orderInfo.paymentDate).toLocaleDateString('vi-VN')}
              </Typography>
            </div>

            <div className="flex justify-between">
              <Typography variant="body1">
                {t('subscription.order.paymentMethod', 'Phương thức thanh toán')}
              </Typography>
              <div className="flex items-center">
                {orderInfo.paymentMethod === PaymentMethod.R_POINT && (
                  <Image
                    src="/src/shared/assets/images/rpoint.png"
                    alt="R-Point"
                    width={16}
                    height={16}
                    className="mr-2"
                  />
                )}
                <Typography variant="body1" className="font-medium">
                  {getPaymentMethodName(orderInfo.paymentMethod)}
                </Typography>
              </div>
            </div>

            <Divider />

            <div className="flex justify-between">
              <Typography variant="body1">
                {t('subscription.order.packageName', 'Tên gói')}
              </Typography>
              <Typography variant="body1" className="font-medium">
                {orderInfo.packageName} (
                {orderInfo.packageType === ServiceType.MAIN
                  ? t('subscription.types.main', 'Gói chính')
                  : t('subscription.types.feature', 'Tính năng')}
                )
              </Typography>
            </div>

            <div className="flex justify-between">
              <Typography variant="body1">
                {t('subscription.order.duration', 'Thời hạn')}
              </Typography>
              <Typography variant="body1" className="font-medium">
                {getDurationLabel(orderInfo.duration)}
              </Typography>
            </div>

            <div className="flex justify-between">
              <Typography variant="body1">{t('subscription.order.price', 'Giá gói')}</Typography>
              <div className="flex items-center">
                <Typography variant="body1" className="font-medium mr-1">
                  {Math.round(orderInfo.price / 1000)}
                </Typography>
                <Image
                  src="/src/shared/assets/images/rpoint.png"
                  alt="R-Point"
                  width={16}
                  height={16}
                />
              </div>
            </div>

            {orderInfo.discount > 0 && (
              <div className="flex justify-between">
                <Typography variant="body1">
                  {t('subscription.order.discount', 'Giảm giá')}
                </Typography>
                <div className="flex items-center">
                  <Typography variant="body1" className="font-medium text-success mr-1">
                    -{Math.round(orderInfo.discount / 1000)}
                  </Typography>
                  <Image
                    src="/src/shared/assets/images/rpoint.png"
                    alt="R-Point"
                    width={16}
                    height={16}
                  />
                </div>
              </div>
            )}

            <Divider />

            <div className="flex justify-between">
              <Typography variant="subtitle1" className="font-bold">
                {t('subscription.order.total', 'Tổng cộng')}
              </Typography>
              <div className="flex items-center">
                <Typography variant="subtitle1" className="font-bold text-primary mr-1">
                  {Math.round(orderInfo.total / 1000)}
                </Typography>
                <Image
                  src="/src/shared/assets/images/rpoint.png"
                  alt="R-Point"
                  width={18}
                  height={18}
                />
              </div>
            </div>
          </div>

          {/* Nút điều hướng */}
          <div className="flex justify-center">
            <Button variant="primary" onClick={handleBackToPackages}>
              {t('subscription.payment.backToPackages', 'Quay lại danh sách gói')}
            </Button>
          </div>
        </Card>
      </ResponsiveGrid>
    </Container>
  );
};

export default SubscriptionPaymentSuccessPage;
