import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import ReminderSelector from '@/modules/calendar/components/reminders/ReminderSelector';
import {
  <PERSON>minder,
  ReminderMethod,
  ReminderTimeUnit,
} from '@/modules/calendar/components/reminders/types';
import { Card, Typography } from '@/shared/components/common';

import { ComponentDemo } from '../../components';

/**
 * Trang demo cho ReminderSelector
 */
const RemindersPage: React.FC = () => {
  const { t } = useTranslation();
  const [reminders, setReminders] = useState<Reminder[]>([
    { id: '1', time: 15, unit: ReminderTimeUnit.MINUTE, method: ReminderMethod.NOTIFICATION },
    { id: '2', time: 1, unit: ReminderTimeUnit.HOUR, method: ReminderMethod.EMAIL },
  ]);
  const [emptyReminders, setEmptyReminders] = useState<Reminder[]>([]);

  return (
    <div className="p-4 sm:p-6 space-y-8">
      <div>
        <h1 className="text-2xl font-semibold mb-2 text-foreground">
          {t('components.calendar.reminders.title', 'Reminder Selector')}
        </h1>
        <p className="text-muted">
          {t('components.calendar.reminders.description', 'Component cấu hình nhắc nhở sự kiện')}
        </p>
      </div>

      <ComponentDemo
        title={t('components.calendar.reminders.basic.title', 'Cấu hình nhắc nhở cơ bản')}
        description={t(
          'components.calendar.reminders.basic.description',
          'Component cấu hình nhắc nhở với các giá trị mặc định'
        )}
        code={`import ReminderSelector from '@/modules/calendar/components/reminders/ReminderSelector';
import { Reminder, ReminderMethod, ReminderTimeUnit } from '@/modules/calendar/components/reminders/types';

const [reminders, setReminders] = useState<Reminder[]>([
  { id: '1', time: 15, unit: ReminderTimeUnit.MINUTE, method: ReminderMethod.NOTIFICATION },
  { id: '2', time: 1, unit: ReminderTimeUnit.HOUR, method: ReminderMethod.EMAIL }
]);

<ReminderSelector
  reminders={reminders}
  onChange={setReminders}
  label="Nhắc nhở"
/>`}
      >
        <Card className="p-6">
          <ReminderSelector
            reminders={reminders}
            onChange={setReminders}
            label={t('components.calendar.reminders.label', 'Nhắc nhở')}
          />

          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
            <Typography variant="h6" className="mb-2">
              {t('components.calendar.reminders.currentValue', 'Giá trị hiện tại')}:
            </Typography>
            <pre className="text-sm overflow-auto p-2 bg-gray-100 dark:bg-gray-900 rounded">
              {JSON.stringify(reminders, null, 2)}
            </pre>
          </div>
        </Card>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.calendar.reminders.empty.title', 'Cấu hình nhắc nhở trống')}
        description={t(
          'components.calendar.reminders.empty.description',
          'Component cấu hình nhắc nhở với danh sách trống'
        )}
        code={`import ReminderSelector from '@/modules/calendar/components/reminders/ReminderSelector';
import { Reminder } from '@/modules/calendar/components/reminders/types';

const [emptyReminders, setEmptyReminders] = useState<Reminder[]>([]);

<ReminderSelector
  reminders={emptyReminders}
  onChange={setEmptyReminders}
  label="Nhắc nhở"
  maxReminders={3}
/>`}
      >
        <Card className="p-6">
          <ReminderSelector
            reminders={emptyReminders}
            onChange={setEmptyReminders}
            label={t('components.calendar.reminders.label', 'Nhắc nhở')}
            maxReminders={3}
          />

          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
            <Typography variant="h6" className="mb-2">
              {t('components.calendar.reminders.currentValue', 'Giá trị hiện tại')}:
            </Typography>
            <pre className="text-sm overflow-auto p-2 bg-gray-100 dark:bg-gray-900 rounded">
              {JSON.stringify(emptyReminders, null, 2)}
            </pre>
          </div>
        </Card>
      </ComponentDemo>

      <ComponentDemo
        title={t(
          'components.calendar.reminders.disabled.title',
          'Cấu hình nhắc nhở bị vô hiệu hóa'
        )}
        description={t(
          'components.calendar.reminders.disabled.description',
          'Component cấu hình nhắc nhở trong trạng thái disabled'
        )}
        code={`import ReminderSelector from '@/modules/calendar/components/reminders/ReminderSelector';
import { Reminder, ReminderMethod, ReminderTimeUnit } from '@/modules/calendar/components/reminders/types';

const [reminders, setReminders] = useState<Reminder[]>([
  { id: '1', time: 15, unit: ReminderTimeUnit.MINUTE, method: ReminderMethod.NOTIFICATION },
  { id: '2', time: 1, unit: ReminderTimeUnit.HOUR, method: ReminderMethod.EMAIL }
]);

<ReminderSelector
  reminders={reminders}
  onChange={setReminders}
  label="Nhắc nhở (Disabled)"
  disabled={true}
/>`}
      >
        <Card className="p-6">
          <ReminderSelector
            reminders={reminders}
            onChange={setReminders}
            label={t('components.calendar.reminders.disabledLabel', 'Nhắc nhở (Disabled)')}
            disabled={true}
          />
        </Card>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.calendar.reminders.usage.title', 'Cách sử dụng')}
        description={t(
          'components.calendar.reminders.usage.description',
          'Hướng dẫn cách sử dụng component ReminderSelector'
        )}
        code={`// Import component
import ReminderSelector from '@/modules/calendar/components/reminders/ReminderSelector';
import { Reminder } from '@/modules/calendar/components/reminders/types';

// State cho danh sách nhắc nhở
const [reminders, setReminders] = useState<Reminder[]>([]);

// Sử dụng component
<ReminderSelector
  reminders={reminders}       // Danh sách nhắc nhở
  onChange={setReminders}     // Callback khi thay đổi danh sách
  label="Nhắc nhở"            // Nhãn hiển thị
  disabled={false}            // Trạng thái disabled
  className="custom-class"    // CSS class bổ sung
  maxReminders={5}            // Số lượng nhắc nhở tối đa
/>`}
      >
        <div className="space-y-4">
          <Typography variant="h6">Props</Typography>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Prop
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Default
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Description
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    value
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    Reminder[]
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    []
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Danh sách nhắc nhở
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    onChange
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    Function
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    -
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Callback khi thay đổi danh sách
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    label
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    string
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    ''
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Nhãn hiển thị
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    maxReminders
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    number
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    5
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Số lượng nhắc nhở tối đa
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default RemindersPage;
