import React, { useState, useCallback } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import { useTranslation } from 'react-i18next';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

import '../../styles/generic-page.css';

import { v4 as uuidv4 } from 'uuid';

import { Card, Button, Icon, Tabs, Input, Typography } from '@/shared/components/common';

import ComponentRegistry from '../../components/ComponentRegistry';
import {
  GenericFormConfig,
  FieldConfig,
  GridConfig,
  ComponentConfig,
} from '../../types/generic-page.types';

import ComponentEditor from './ComponentEditor';
import ComponentPalette from './ComponentPalette';
import PagePreview from './PagePreview';

// Create responsive grid layout with width provider
const ResponsiveGridLayout = WidthProvider(Responsive);

interface PageBuilderProps {
  initialConfig?: GenericFormConfig;
  onSave: (config: GenericFormConfig) => void;
  onPreview: (config: GenericFormConfig) => void;
  onCancel: () => void;
}

/**
 * PageBuilder component for creating and editing generic pages
 */
const PageBuilder: React.FC<PageBuilderProps> = ({
  initialConfig,
  onSave,
  onPreview,
  onCancel,
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('editor');
  const [selectedComponentId, setSelectedComponentId] = useState<string | null>(null);
  const [pageConfig, setPageConfig] = useState<GenericFormConfig>(() => {
    if (initialConfig) {return initialConfig;}

    // Default empty config
    return {
      formId: `form-${uuidv4()}`,
      title: 'New Page',
      subtitle: 'Page description',
      groups: [
        {
          id: `group-${uuidv4()}`,
          label: 'Default Group',
          fields: [],
        },
      ],
      uiConfig: {
        layout: 'card',
        spacing: 'normal',
        showGroupHeaders: true,
      },
    };
  });

  // Get all fields from all groups
  const allFields = pageConfig.groups.flatMap(group => group.fields);

  // Find selected component
  const selectedComponent = allFields.find(field => field.grid.i === selectedComponentId);

  // Create layout for react-grid-layout
  const layouts = {
    lg: allFields.map(field => field.grid),
    md: allFields.map(field => field.grid),
    sm: allFields.map(field => field.grid),
    xs: allFields.map(field => field.grid),
  };

  // Handle adding a new component
  const handleAddComponent = useCallback((componentType: string) => {
    const componentId = `${componentType}-${uuidv4()}`;

    // Default grid config
    const gridConfig: GridConfig = {
      i: componentId,
      x: 0,
      y: Infinity, // Put at the bottom
      w: 6,
      h: 2,
      minW: 2,
      minH: 1,
    };

    // Default component config
    const componentConfig: ComponentConfig = {
      id: componentId,
      label: `New ${componentType}`,
      type: 'text',
      required: false,
      placeholder: `Enter ${componentType}...`,
    };

    // Create new field
    const newField: FieldConfig = {
      component: componentType,
      config: componentConfig,
      grid: gridConfig,
    };

    // Add to first group
    setPageConfig(prev => {
      const updatedGroups = [...prev.groups];
      updatedGroups[0].fields.push(newField);
      return {
        ...prev,
        groups: updatedGroups,
      };
    });

    // Select the new component
    setSelectedComponentId(componentId);
  }, []);

  // Handle layout change
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleLayoutChange = useCallback((currentLayout: any[]) => {
    setPageConfig(prev => {
      const updatedGroups = prev.groups.map(group => {
        const updatedFields = group.fields.map(field => {
          const layoutItem = currentLayout.find(item => item.i === field.grid.i);
          if (layoutItem) {
            return {
              ...field,
              grid: {
                ...field.grid,
                x: layoutItem.x,
                y: layoutItem.y,
                w: layoutItem.w,
                h: layoutItem.h,
              },
            };
          }
          return field;
        });

        return {
          ...group,
          fields: updatedFields,
        };
      });

      return {
        ...prev,
        groups: updatedGroups,
      };
    });
  }, []);

  // Handle component selection
  const handleSelectComponent = useCallback((componentId: string) => {
    setSelectedComponentId(componentId);
  }, []);

  // Handle component deletion
  const handleDeleteComponent = useCallback(
    (componentId: string) => {
      setPageConfig(prev => {
        const updatedGroups = prev.groups.map(group => {
          return {
            ...group,
            fields: group.fields.filter(field => field.grid.i !== componentId),
          };
        });

        return {
          ...prev,
          groups: updatedGroups,
        };
      });

      if (selectedComponentId === componentId) {
        setSelectedComponentId(null);
      }
    },
    [selectedComponentId]
  );

  // Handle component update
  const handleUpdateComponent = useCallback(
    (componentId: string, updates: Partial<FieldConfig>) => {
      setPageConfig(prev => {
        const updatedGroups = prev.groups.map(group => {
          const updatedFields = group.fields.map(field => {
            if (field.grid.i === componentId) {
              return {
                ...field,
                ...updates,
                config: {
                  ...field.config,
                  ...(updates.config || {}),
                },
                grid: {
                  ...field.grid,
                  ...(updates.grid || {}),
                },
              };
            }
            return field;
          });

          return {
            ...group,
            fields: updatedFields,
          };
        });

        return {
          ...prev,
          groups: updatedGroups,
        };
      });
    },
    []
  );

  // Handle page metadata update
  const handleUpdatePageMetadata = useCallback((updates: Partial<GenericFormConfig>) => {
    setPageConfig(prev => ({
      ...prev,
      ...updates,
    }));
  }, []);

  // Các hàm quản lý nhóm - sẽ được sử dụng trong tương lai
  // Hiện tại đã comment để tránh lỗi ESLint
  /*
  // Handle group update
  const handleUpdateGroup = useCallback((groupId: string, updates: Partial<GroupConfig>) => {
    setPageConfig(prev => {
      const updatedGroups = prev.groups.map(group => {
        if (group.id === groupId) {
          return {
            ...group,
            ...updates
          };
        }
        return group;
      });

      return {
        ...prev,
        groups: updatedGroups
      };
    });
  }, []);

  // Handle adding a new group
  const handleAddGroup = useCallback(() => {
    const newGroup: GroupConfig = {
      id: `group-${uuidv4()}`,
      label: `Group ${pageConfig.groups.length + 1}`,
      fields: []
    };

    setPageConfig(prev => ({
      ...prev,
      groups: [...prev.groups, newGroup]
    }));
  }, [pageConfig.groups.length]);

  // Handle deleting a group
  const handleDeleteGroup = useCallback((groupId: string) => {
    setPageConfig(prev => {
      // Don't delete if it's the only group
      if (prev.groups.length <= 1) return prev;

      // Move fields to the first group
      const groupToDelete = prev.groups.find(g => g.id === groupId);
      const firstGroup = prev.groups.find(g => g.id !== groupId);

      if (!groupToDelete || !firstGroup) return prev;

      const updatedGroups = prev.groups
        .filter(group => group.id !== groupId)
        .map(group => {
          if (group.id === firstGroup.id) {
            return {
              ...group,
              fields: [...group.fields, ...groupToDelete.fields]
            };
          }
          return group;
        });

      return {
        ...prev,
        groups: updatedGroups
      };
    });
  }, []);
  */

  return (
    <div className="page-builder">
      <div className="flex justify-between items-center mb-6">
        <div>
          <Input
            value={pageConfig.title}
            onChange={e => handleUpdatePageMetadata({ title: e.target.value })}
            className="text-2xl font-bold border-none focus:ring-0 px-0 h-auto"
            placeholder={t('genericPage.pageBuilder.titlePlaceholder', 'Page Title')}
          />
          <Input
            value={pageConfig.subtitle || ''}
            onChange={e => handleUpdatePageMetadata({ subtitle: e.target.value })}
            className="text-muted border-none focus:ring-0 px-0 h-auto"
            placeholder={t('genericPage.pageBuilder.subtitlePlaceholder', 'Page Description')}
          />
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={onCancel}>
            {t('genericPage.pageBuilder.cancel', 'Cancel')}
          </Button>
          <Button variant="outline" onClick={() => onPreview(pageConfig)} leftIcon="eye">
            {t('genericPage.pageBuilder.preview', 'Preview')}
          </Button>
          <Button variant="primary" onClick={() => onSave(pageConfig)} leftIcon="save">
            {t('genericPage.pageBuilder.save', 'Save')}
          </Button>
        </div>
      </div>

      <Tabs
        items={[
          {
            key: 'editor',
            label: t('genericPage.pageBuilder.editorTab', 'Editor'),
            children: (
              <div className="mt-4 grid grid-cols-1 lg:grid-cols-4 gap-6">
                {/* Component Palette */}
                <div className="lg:col-span-1">
                  <ComponentPalette onAddComponent={handleAddComponent} />
                </div>

                {/* Page Layout Editor */}
                <div className="lg:col-span-2">
                  <Card className="w-full">
                    <div className="p-4 border-b">
                      <Typography variant="h3">
                        {t('genericPage.pageBuilder.layoutEditor', 'Layout Editor')}
                      </Typography>
                    </div>
                    <div className="p-4 bg-gray-50 dark:bg-gray-900/50 min-h-[500px]">
                      {allFields.length === 0 ? (
                        <div className="flex flex-col items-center justify-center h-[400px] border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                          <Icon name="layout" size="xl" className="mb-4 text-muted" />
                          <Typography variant="h4" className="mb-2">
                            {t(
                              'genericPage.pageBuilder.emptyState.title',
                              'Start Building Your Page'
                            )}
                          </Typography>
                          <Typography className="text-muted mb-4">
                            {t(
                              'genericPage.pageBuilder.emptyState.description',
                              'Drag components from the palette to add them to your page'
                            )}
                          </Typography>
                          <Button
                            variant="outline"
                            onClick={() => handleAddComponent('text-input')}
                            leftIcon="plus"
                          >
                            {t('genericPage.pageBuilder.emptyState.addComponent', 'Add Component')}
                          </Button>
                        </div>
                      ) : (
                        <ResponsiveGridLayout
                          className="layout page-builder"
                          layouts={layouts}
                          breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480 }}
                          cols={{ lg: 12, md: 12, sm: 6, xs: 4 }}
                          rowHeight={60}
                          containerPadding={[0, 0]}
                          margin={[16, 16]}
                          onLayoutChange={handleLayoutChange}
                          isDraggable
                          isResizable
                          draggableHandle=".component-drag-handle"
                          useCSSTransforms={true}
                          preventCollision={true}
                        >
                          {allFields.map(field => {
                            const Component = ComponentRegistry[field.component];
                            const isSelected = field.grid.i === selectedComponentId;

                            return (
                              <div
                                key={field.grid.i}
                                className={`border-2 ${isSelected ? 'border-primary' : 'border-transparent'} hover:border-primary/50 transition-colors duration-200 rounded-md overflow-hidden`}
                                onClick={() => handleSelectComponent(field.grid.i)}
                                data-grid={field.grid}
                              >
                                <div className="bg-white dark:bg-gray-800 h-full flex flex-col">
                                  <div className="p-2 bg-gray-100 dark:bg-gray-700 flex justify-between items-center component-drag-handle cursor-move">
                                    <Typography variant="body2" className="font-medium truncate">
                                      {field.config.label || field.component}
                                    </Typography>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-6 w-6 p-0"
                                      onClick={e => {
                                        e.stopPropagation();
                                        handleDeleteComponent(field.grid.i);
                                      }}
                                    >
                                      <Icon name="trash-2" size="sm" />
                                    </Button>
                                  </div>
                                  <div className="p-2 flex-1 flex items-center justify-center">
                                    {Component ? (
                                      <div className="text-muted text-sm">
                                        {field.component}: {field.config.label}
                                      </div>
                                    ) : (
                                      <div className="text-muted text-sm">
                                        {t(
                                          'genericPage.pageBuilder.unknownComponent',
                                          'Unknown component: {{component}}',
                                          { component: field.component }
                                        )}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </ResponsiveGridLayout>
                      )}
                    </div>
                  </Card>
                </div>

                {/* Component Properties */}
                <div className="lg:col-span-1">
                  {selectedComponent ? (
                    <ComponentEditor
                      component={selectedComponent}
                      onUpdate={(updates: Partial<FieldConfig>) =>
                        handleUpdateComponent(selectedComponent.grid.i, updates)
                      }
                      onDelete={() => handleDeleteComponent(selectedComponent.grid.i)}
                    />
                  ) : (
                    <Card className="w-full">
                      <div className="p-4 border-b">
                        <Typography variant="h3">
                          {t('genericPage.pageBuilder.properties', 'Properties')}
                        </Typography>
                      </div>
                      <div className="p-4 text-center text-muted">
                        {t(
                          'genericPage.pageBuilder.selectComponent',
                          'Select a component to edit its properties'
                        )}
                      </div>
                    </Card>
                  )}
                </div>
              </div>
            ),
          },
          {
            key: 'preview',
            label: t('genericPage.pageBuilder.previewTab', 'Preview'),
            children: (
              <div className="mt-4">
                <PagePreview config={pageConfig} />
              </div>
            ),
          },
          {
            key: 'code',
            label: t('genericPage.pageBuilder.codeTab', 'JSON'),
            children: (
              <div className="mt-4">
                <Card className="w-full">
                  <div className="p-4 border-b flex justify-between items-center">
                    <Typography variant="h3">
                      {t('genericPage.pageBuilder.jsonConfig', 'JSON Configuration')}
                    </Typography>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(JSON.stringify(pageConfig, null, 2));
                      }}
                      leftIcon="clipboard"
                    >
                      {t('genericPage.pageBuilder.copyJson', 'Copy JSON')}
                    </Button>
                  </div>
                  <div className="p-4 bg-gray-900 text-gray-100 rounded-b-lg overflow-auto max-h-[600px]">
                    <pre className="text-sm font-mono">{JSON.stringify(pageConfig, null, 2)}</pre>
                  </div>
                </Card>
              </div>
            ),
          },
        ]}
        activeKey={activeTab}
        onChange={setActiveTab}
      />
    </div>
  );
};

export default PageBuilder;
