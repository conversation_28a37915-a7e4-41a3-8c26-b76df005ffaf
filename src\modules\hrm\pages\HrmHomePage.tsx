import React from 'react';
import { useTranslation } from 'react-i18next';

import ModuleCard from '@/modules/components/card/ModuleCard';
import { Typography } from '@/shared/components/common';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid';

/**
 * Trang chủ module HRM
 */
const HrmHomePage: React.FC = () => {
  const { t } = useTranslation(['common', 'hrm']);

  // Danh sách các module con của HRM
  const subModules = [
    {
      id: 'employees',
      title: t('hrm:modules.employees.title', 'Nhân viên'),
      description: t('hrm:modules.employees.description', 'Quản lý thông tin nhân viên'),
      icon: 'users',
      count: 25,
      countLabel: t('hrm:modules.employees.countLabel', 'Nhân viên'),
      linkTo: '/hrm/employees',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'departments',
      title: t('hrm:modules.departments.title', 'Phòng ban'),
      description: t('hrm:modules.departments.description', 'Quản lý cơ cấu tổ chức'),
      icon: 'building',
      count: 12,
      countLabel: t('hrm:modules.departments.countLabel', 'Phòng ban'),
      linkTo: '/hrm/departments',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'permissions',
      title: t('hrm:modules.permissions.title', 'Phân quyền'),
      description: t('hrm:modules.permissions.description', 'Quản lý quyền hạn người dùng'),
      icon: 'lock',
      count: 18,
      countLabel: t('hrm:modules.permissions.countLabel', 'Quyền'),
      linkTo: '/hrm/permissions',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'recruitment',
      title: t('hrm:modules.recruitment.title', 'Tuyển dụng'),
      description: t('hrm:modules.recruitment.description', 'Quản lý quy trình tuyển dụng'),
      icon: 'plus',
      count: 8,
      countLabel: t('hrm:modules.recruitment.countLabel', 'Vị trí'),
      linkTo: '/hrm/recruitment',
      linkText: t('common:view', 'Xem'),
      disabled: true,
    },
    {
      id: 'attendance',
      title: t('hrm:modules.attendance.title', 'Chấm công'),
      description: t('hrm:modules.attendance.description', 'Quản lý chấm công và giờ làm'),
      icon: 'calendar',
      count: 22,
      countLabel: t('hrm:modules.attendance.countLabel', 'Ngày'),
      linkTo: '/hrm/attendance',
      linkText: t('common:view', 'Xem'),
      disabled: true,
    },
    {
      id: 'leave',
      title: t('hrm:modules.leave.title', 'Nghỉ phép'),
      description: t('hrm:modules.leave.description', 'Quản lý nghỉ phép và vắng mặt'),
      icon: 'calendar',
      count: 5,
      countLabel: t('hrm:modules.leave.countLabel', 'Yêu cầu'),
      linkTo: '/hrm/leave',
      linkText: t('common:view', 'Xem'),
      disabled: true,
    },
    {
      id: 'payroll',
      title: t('hrm:modules.payroll.title', 'Lương thưởng'),
      description: t('hrm:modules.payroll.description', 'Quản lý lương và phúc lợi'),
      icon: 'credit-card',
      count: 1,
      countLabel: t('hrm:modules.payroll.countLabel', 'Kỳ lương'),
      linkTo: '/hrm/payroll',
      linkText: t('common:view', 'Xem'),
      disabled: true,
    },
    {
      id: 'training',
      title: t('hrm:modules.training.title', 'Đào tạo'),
      description: t('hrm:modules.training.description', 'Quản lý đào tạo và phát triển'),
      icon: 'document',
      count: 4,
      countLabel: t('hrm:modules.training.countLabel', 'Khóa học'),
      linkTo: '/hrm/training',
      linkText: t('common:view', 'Xem'),
      disabled: true,
    },
  ];

  return (
    <div>
      <div className="mb-8">
        <Typography variant="h3" className="mb-2">
          {t('hrm:title', 'Quản lý nhân sự (HRM)')}
        </Typography>
        <Typography variant="body1" color="muted">
          {t('hrm:description', 'Quản lý nhân sự và tuyển dụng')}
        </Typography>
      </div>

      <ResponsiveGrid
        gap={4}
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
      >
        {subModules.map(module => (
          <ModuleCard
            key={module.id}
            title={module.title}
            description={module.description}
            icon={module.icon}
            count={module.count}
            countLabel={module.countLabel}
            linkTo={module.linkTo}
            linkText={module.linkText}
            className="h-full"
            disabled={module.disabled}
          />
        ))}
      </ResponsiveGrid>
    </div>
  );
};

export default HrmHomePage;
