import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Typography, Icon } from '@/shared/components/common';
import {
  AsyncSelect,
  CreatableSelect,
  ComboboxSelect,
  TypeaheadSelect,
} from '@/shared/components/common/Select';

import type { SelectOption } from '@/shared/components/common/Select/Select';

/**
 * Trang demo cho các component Select nâng cao
 */
const AdvancedSelectDemo: React.FC = () => {
  const { t } = useTranslation();
  const [asyncValue, setAsyncValue] = useState<string>('');
  const [creatableValue, setCreatableValue] = useState<string>('');
  const [comboboxValue, setComboboxValue] = useState<string>('');
  const [typeaheadValue, setTypeaheadValue] = useState<string>('');
  const [typeaheadMultiValue, setTypeaheadMultiValue] = useState<string[]>([]);

  // Options cho CreatableSelect
  const creatableOptions: SelectOption[] = [
    { value: 'chocolate', label: 'Chocolate' },
    { value: 'strawberry', label: 'Strawberry' },
    { value: 'vanilla', label: 'Vanilla' },
  ];

  // Options cho ComboboxSelect
  const comboboxOptions: SelectOption[] = [
    { value: 'red', label: 'Red' },
    { value: 'green', label: 'Green' },
    { value: 'blue', label: 'Blue' },
    { value: 'yellow', label: 'Yellow' },
    { value: 'purple', label: 'Purple' },
  ];

  // Options cho TypeaheadSelect
  const typeaheadOptions: SelectOption[] = [
    { value: 'javascript', label: 'JavaScript', icon: <Icon name="code" size="sm" /> },
    { value: 'typescript', label: 'TypeScript', icon: <Icon name="code" size="sm" /> },
    { value: 'python', label: 'Python', icon: <Icon name="code" size="sm" /> },
    { value: 'java', label: 'Java', icon: <Icon name="code" size="sm" /> },
    { value: 'csharp', label: 'C#', icon: <Icon name="code" size="sm" /> },
    { value: 'php', label: 'PHP', icon: <Icon name="code" size="sm" /> },
    { value: 'ruby', label: 'Ruby', icon: <Icon name="code" size="sm" /> },
    { value: 'swift', label: 'Swift', icon: <Icon name="code" size="sm" /> },
    { value: 'kotlin', label: 'Kotlin', icon: <Icon name="code" size="sm" /> },
    { value: 'go', label: 'Go', icon: <Icon name="code" size="sm" /> },
  ];

  // Simulate API call for AsyncSelect
  const loadOptions = async (inputValue: string): Promise<SelectOption[]> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock API response
    const mockData = [
      { value: 'new-york', label: 'New York' },
      { value: 'london', label: 'London' },
      { value: 'paris', label: 'Paris' },
      { value: 'tokyo', label: 'Tokyo' },
      { value: 'sydney', label: 'Sydney' },
      { value: 'berlin', label: 'Berlin' },
      { value: 'rome', label: 'Rome' },
      { value: 'madrid', label: 'Madrid' },
      { value: 'amsterdam', label: 'Amsterdam' },
      { value: 'moscow', label: 'Moscow' },
    ];

    // Filter based on input
    return mockData.filter(item => item.label.toLowerCase().includes(inputValue.toLowerCase()));
  };

  return (
    <div className="space-y-8">
      <Typography variant="h1">Advanced Select Components</Typography>
      <Typography>
        A collection of advanced select components with various features for different use cases.
      </Typography>

      {/* AsyncSelect */}
      <div className="bg-white dark:bg-dark p-6 rounded-lg shadow-sm mb-8">
        <h2 className="text-xl font-semibold mb-2">{t('components.inputs.asyncSelect.title')}</h2>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          {t('components.inputs.asyncSelect.description')}
        </p>

        <div className="w-full max-w-md mx-auto mb-8">
          <AsyncSelect
            label="Search for a city"
            value={asyncValue}
            onChange={val => setAsyncValue(val as string)}
            loadOptions={loadOptions}
            placeholder="Type to search cities..."
            debounceTime={300}
            noOptionsMessage="No cities found"
            loadingMessage="Searching cities..."
          />
          {asyncValue && (
            <div className="mt-2 text-sm">
              Selected city: <span className="font-medium">{asyncValue}</span>
            </div>
          )}
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium mb-3">Code Example</h3>
          <pre className="bg-gray-50 dark:bg-gray-800 p-4 rounded-md overflow-x-auto text-sm">
            <code>{`import { AsyncSelect } from '@/shared/components/common/Select';

// Function to load options from API
const loadOptions = async (inputValue: string): Promise<SelectOption[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Mock API response
  const mockData = [
    { value: 'new-york', label: 'New York' },
    { value: 'london', label: 'London' },
    // ... more cities
  ];

  // Filter based on input
  return mockData.filter(item =>
    item.label.toLowerCase().includes(inputValue.toLowerCase())
  );
};

// State
const [value, setValue] = useState('');

// Render
<AsyncSelect
  label="Search for a city"
  value={value}
  onChange={(val) => setValue(val as string)}
  loadOptions={loadOptions}
  placeholder="Type to search cities..."
  debounceTime={300}
  noOptionsMessage="No cities found"
  loadingMessage="Searching cities..."
/>`}</code>
          </pre>
        </div>
      </div>

      {/* CreatableSelect */}
      <div className="bg-white dark:bg-dark p-6 rounded-lg shadow-sm mb-8">
        <h2 className="text-xl font-semibold mb-2">
          {t('components.inputs.creatableSelect.title')}
        </h2>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          {t('components.inputs.creatableSelect.description')}
        </p>

        <div className="w-full max-w-md mx-auto mb-8">
          <CreatableSelect
            label="Select or create a flavor"
            value={creatableValue}
            onChange={val => setCreatableValue(val as string)}
            options={creatableOptions}
            placeholder="Choose or create a flavor..."
            formatCreateLabel={inputValue => `Create "${inputValue}"`}
            onCreateOption={inputValue => console.log(`Created: ${inputValue}`)}
          />
          {creatableValue && (
            <div className="mt-2 text-sm">
              Selected flavor: <span className="font-medium">{creatableValue}</span>
            </div>
          )}
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium mb-3">Code Example</h3>
          <pre className="bg-gray-50 dark:bg-gray-800 p-4 rounded-md overflow-x-auto text-sm">
            <code>{`import { CreatableSelect } from '@/shared/components/common/Select';

// Options
const options = [
  { value: 'chocolate', label: 'Chocolate' },
  { value: 'strawberry', label: 'Strawberry' },
  { value: 'vanilla', label: 'Vanilla' },
];

// State
const [value, setValue] = useState('');

// Render
<CreatableSelect
  label="Select or create a flavor"
  value={value}
  onChange={(val) => setValue(val as string)}
  options={options}
  placeholder="Choose or create a flavor..."
  formatCreateLabel={(inputValue) => \`Create "\${inputValue}"\`}
  onCreateOption={(inputValue) => console.log(\`Created: \${inputValue}\`)}
/>`}</code>
          </pre>
        </div>
      </div>

      {/* ComboboxSelect */}
      <div className="bg-white dark:bg-dark p-6 rounded-lg shadow-sm mb-8">
        <h2 className="text-xl font-semibold mb-2">
          {t('components.inputs.comboboxSelect.title')}
        </h2>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          {t('components.inputs.comboboxSelect.description')}
        </p>

        <div className="w-full max-w-md mx-auto mb-8">
          <ComboboxSelect
            label="Select or type a color"
            value={comboboxValue}
            onChange={val => setComboboxValue(val)}
            options={comboboxOptions}
            placeholder="Choose or type a color..."
            allowCustomValue={true}
          />
          {comboboxValue && (
            <div className="mt-2 text-sm">
              Selected/entered color: <span className="font-medium">{comboboxValue}</span>
            </div>
          )}
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium mb-3">Code Example</h3>
          <pre className="bg-gray-50 dark:bg-gray-800 p-4 rounded-md overflow-x-auto text-sm">
            <code>{`import { ComboboxSelect } from '@/shared/components/common/Select';

// Options
const options = [
  { value: 'red', label: 'Red' },
  { value: 'green', label: 'Green' },
  { value: 'blue', label: 'Blue' },
  { value: 'yellow', label: 'Yellow' },
  { value: 'purple', label: 'Purple' },
];

// State
const [value, setValue] = useState('');

// Render
<ComboboxSelect
  label="Select or type a color"
  value={value}
  onChange={(val) => setValue(val)}
  options={options}
  placeholder="Choose or type a color..."
  allowCustomValue={true}
/>`}</code>
          </pre>
        </div>
      </div>

      {/* TypeaheadSelect */}
      <div className="bg-white dark:bg-dark p-6 rounded-lg shadow-sm mb-8">
        <h2 className="text-xl font-semibold mb-2">
          {t('components.inputs.typeaheadSelect.title')}
        </h2>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          {t('components.inputs.typeaheadSelect.description')}
        </p>

        <div className="w-full max-w-md mx-auto mb-8">
          <TypeaheadSelect
            label="Select a programming language"
            value={typeaheadValue}
            onChange={val => setTypeaheadValue(val as string)}
            options={typeaheadOptions}
            placeholder="Type to search..."
            highlightMatch={true}
            maxSuggestions={5}
          />
          {typeaheadValue && (
            <div className="mt-2 text-sm">
              Selected language: <span className="font-medium">{typeaheadValue}</span>
            </div>
          )}
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium mb-3">Code Example</h3>
          <pre className="bg-gray-50 dark:bg-gray-800 p-4 rounded-md overflow-x-auto text-sm">
            <code>{`import { TypeaheadSelect } from '@/shared/components/common/Select';
import { Icon } from '@/shared/components/common';

// Options
const options = [
  { value: 'javascript', label: 'JavaScript', icon: <Icon name="code" size="sm" /> },
  { value: 'typescript', label: 'TypeScript', icon: <Icon name="code" size="sm" /> },
  // ... more programming languages
];

// State
const [value, setValue] = useState('');

// Render
<TypeaheadSelect
  label="Select a programming language"
  value={value}
  onChange={(val) => setValue(val as string)}
  options={options}
  placeholder="Type to search..."
  highlightMatch={true}
  maxSuggestions={5}
/>`}</code>
          </pre>
        </div>
      </div>

      {/* TypeaheadSelect (Multiple) */}
      <div className="bg-white dark:bg-dark p-6 rounded-lg shadow-sm mb-8">
        <h2 className="text-xl font-semibold mb-2">Multiple Typeahead Select</h2>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          Typeahead Select with multiple selection
        </p>

        <div className="w-full max-w-md mx-auto mb-8">
          <TypeaheadSelect
            label="Select multiple programming languages"
            value={typeaheadMultiValue}
            onChange={val => setTypeaheadMultiValue(val as string[])}
            options={typeaheadOptions}
            placeholder="Type to search..."
            multiple={true}
            highlightMatch={true}
            maxSuggestions={5}
          />
          {typeaheadMultiValue.length > 0 && (
            <div className="mt-2 text-sm">
              Selected languages:{' '}
              <span className="font-medium">{typeaheadMultiValue.join(', ')}</span>
            </div>
          )}
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium mb-3">Code Example</h3>
          <pre className="bg-gray-50 dark:bg-gray-800 p-4 rounded-md overflow-x-auto text-sm">
            <code>{`import { TypeaheadSelect } from '@/shared/components/common/Select';

// State
const [value, setValue] = useState<string[]>([]);

// Render
<TypeaheadSelect
  label="Select multiple programming languages"
  value={value}
  onChange={(val) => setValue(val as string[])}
  options={options}
  placeholder="Type to search..."
  multiple={true}
  highlightMatch={true}
  maxSuggestions={5}
/>`}</code>
          </pre>
        </div>
      </div>
    </div>
  );
};

export default AdvancedSelectDemo;
