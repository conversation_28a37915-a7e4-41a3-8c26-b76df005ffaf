{"common": {"loading": "Loading...", "error": "An error occurred", "retry": "Retry", "cancel": "Cancel", "save": "Save", "ok": "OK", "delete": "Delete", "edit": "Edit", "create": "Create", "search": "Search", "filter": "Filter", "sort": "Sort", "settings": "Settings", "profile": "Profile", "logout": "Logout", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "custom": "Custom", "open": "Open", "add": "Add", "close": "Close", "reset": "Reset", "menu": "<PERSON><PERSON>", "date": "Date", "dateRange": "Date Range", "columns": "Columns", "selectAll": "Select All", "startDate": "Start Date", "endDate": "End Date", "code": "Code", "name": "Name", "discountValue": "Discount Value", "minimumOrderValue": "Minimum Order Value", "maximumDiscountAmount": "Maximum Discount Amount", "status": "Status", "addNew": "Add New", "searchPlaceholder": "Search...", "all": "All", "backToHome": "Back to Home", "level": "Level", "activate": "Activate", "deactivate": "Deactivate", "details": "View Details", "home": "Home", "componentsText": "Components", "viewExample": "View Example", "animation": "Animation", "products": "Products", "services": "Services", "service1": "Service 1", "service2": "Service 2", "service3": "Service 3", "contact": "Contact", "inbox": "Inbox", "notifications": "Notifications", "help": "Help & Support", "noResults": "No results found", "forbidden": {"title": "Access Denied", "message": "You don't have permission to access this page. Please contact your administrator if you believe this is an error."}, "goBack": "Go Back", "login": "<PERSON><PERSON>", "aiAgents": {"title": "AI Agents", "description": "Manage your AI agents and assistants", "noAgentsFound": "No agents found matching your search criteria"}, "backToButtons": "Back to Buttons", "backToFormSections": "Back to Form Sections", "expandAll": "Expand All", "collapseAll": "Collapse All", "primary": "Primary", "secondary": "Secondary", "outline": "Outline", "ghost": "Ghost", "success": "Success", "warning": "Warning", "danger": "Danger", "small": "Small", "medium": "Medium", "large": "Large", "disabled": "Disabled", "createNew": "Create New", "nextStep": "Next Step", "action": "Action", "ui": {"card": {"title": "Card Title", "subtitle": "Subtitle", "content": "This is the content of the card.", "footer": "Footer", "loading": "Loading...", "withFooter": {"content": "This card has a footer with action buttons."}, "withIcon": {"content": "This card has an icon in the header."}, "bordered": {"content": "This card has a border."}, "customHeader": {"content": "This card has a custom header."}}, "badge": {"new": "New", "count": "{{count}}"}, "tabs": {"tab": "Tab {{number}}", "content": "Tab {{number}} content"}, "accordion": {"item": "Item {{number}}", "content": "Item {{number}} content", "expandAll": "Expand All", "collapseAll": "Collapse All"}}}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "username": "Username", "fullName": "Full Name", "phone": "Phone Number", "rememberMe": "Remember Me", "signIn": "Sign In", "signUp": "Sign Up", "orContinueWith": "Or continue with", "forgotPasswordDescription": "Enter your email address and we'll send you a link to reset your password.", "loginError": "<PERSON><PERSON> failed. Please check your credentials and try again.", "registerError": "Registration failed. Please try again later.", "recaptchaError": "Please verify that you are not a robot.", "verifyAccount": "Verify Account", "verifyEmailDescription": "We've sent a verification code to your email {{email}}. Please check your inbox.", "verifySmsDescription": "We've sent a verification code to your phone {{phone}}. Please check your messages.", "verifyDescription": "We've sent a verification code. Please check your email or messages.", "resendCode": "Resend Code", "codeSent": "Verification code has been sent.", "resendFailed": "Failed to resend verification code. Please try again.", "backToLogin": "Back to Login", "twoFactorAuth": "Two-Factor Authentication", "twoFactorAuthDescription": "Please enter the verification code to continue.", "verificationCode": "Verification Code", "verify": "Verify", "didntReceiveCode": "Didn't receive the code?", "passwordRequirements": "Password must be at least 8 characters long, including uppercase, lowercase, numbers, and special characters."}, "chat": {"newChat": "New Chat", "sendMessage": "Send Message", "typeMessage": "Type a message...", "typeSlashForMenu": "Type / to quickly access menu...", "uploadFile": "Upload File", "uploadFromComputer": "Upload from Computer", "uploadFromGoogleDrive": "Add from Google Drive", "webSearch": "Web Search", "voiceInput": "Voice Input", "aiAgents": "AI Agents", "aiAssistants": "AI Assistants", "specializedAgents": "Specialized Agents", "selectAgent": "Select Agent", "maxFilesExceeded": "Maximum {{max}} files allowed", "invalidFileTypes": "Some files were not uploaded because they are not supported. Allowed formats: images, PDF, XLSX, CSV, DOCX, JSON, MD, JSONL", "errorProcessingFiles": "Error processing files. Please try again.", "chooseFeature": "Choose a feature to use", "payment": "Chatbot Payment", "imagePasted": "Image pasted from clipboard"}, "marketing": {"title": "Marketing", "description": "Manage marketing campaigns, advertisements, and promotions"}, "data": {"title": "Data & Analytics", "description": "View and analyze data, reports, and statistics"}, "marketplace": {"title": "Marketplace", "description": "Explore and shop for products, services, and resources"}, "viewPanel": {"welcome": "Welcome to AI ERP", "recentChats": "Recent Chats", "favorites": "Favorites", "history": "History", "noData": "No data available", "loadMore": "Load More"}, "settings": {"account": "Account", "appearance": "Appearance", "notifications": "Notifications", "privacy": "Privacy", "language": "Language", "about": "About"}, "errors": {"required": "This field is required", "invalidEmail": "Invalid email address", "passwordMismatch": "Passwords do not match", "minLength": "Minimum {{count}} characters", "maxLength": "Maximum {{count}} characters", "serverError": "Server error, please try again later", "networkError": "Network error, please check your connection"}, "validation": {"required": "{{field}} is required", "email": "Invalid email address", "minLength": "{{field}} must be at least {{length}} characters", "maxLength": "{{field}} must not exceed {{length}} characters", "phone": "Phone number must be 10-15 digits", "passwordUppercase": "Password must contain at least one uppercase letter", "passwordLowercase": "Password must contain at least one lowercase letter", "passwordNumber": "Password must contain at least one number", "passwordSpecial": "Password must contain at least one special character", "passwordsMatch": "Password and confirm password do not match", "emailOrPhone": "Invalid email or phone number"}, "components": {"library": {"title": "Components Library", "description": "Library of shared components for the RedAI system. Components are designed in a modern style, with full support for light/dark mode and responsive design."}, "charts": {"demo": {"title": "Chart Demo", "description": "Chart components with responsive design, multilingual support, and theme compatibility."}, "lineChart": {"title": "Line Chart", "description": "LineChart component displays data as lines, supporting multiple data lines, tooltips, and legends.", "basic": {"title": "Basic Line Chart", "description": "Basic line chart with a single data line."}, "multiLine": {"title": "Multi-line Chart", "description": "Line chart with multiple data lines."}, "customized": {"title": "Customized Line Chart", "description": "Line chart with customizations like line type, stroke width, and dot display."}}}, "tooltip": {"title": "Modern Tooltip", "description": "Modern tooltip component with various positions and styles.", "basic": {"title": "Basic Tooltip", "description": "Basic tooltip with default settings.", "content": "This is a tooltip", "button": "Hover me"}, "positions": {"title": "Tooltip Positions", "description": "Tooltips can be displayed in different positions.", "top": "Top tooltip", "topButton": "Top", "right": "Right tooltip", "rightButton": "Right", "bottom": "Bottom tooltip", "bottomButton": "Bottom", "left": "Left tooltip", "leftButton": "Left"}, "variants": {"title": "<PERSON><PERSON><PERSON>", "description": "Tooltips with different styles.", "dark": {"content": "Dark tooltip", "button": "Dark"}, "light": {"content": "Light tooltip", "button": "Light"}}, "sizes": {"title": "Tooltip <PERSON>", "description": "Tooltips in different sizes.", "small": {"content": "Small tooltip"}, "medium": {"content": "Medium tooltip"}, "large": {"content": "Large tooltip"}}, "withIcons": {"title": "Tooltip with Icons", "description": "Tooltips used with icon buttons.", "add": "Add new item"}, "noArrow": {"title": "<PERSON>lt<PERSON> without Arrow", "description": "Tooltips can be displayed without an arrow.", "content": "Tooltip without arrow", "button": "No Arrow"}}, "searchBar": {"title": "Search Bar", "description": "Modern search bar component with animation and various styles.", "basic": {"title": "Basic Search Bar", "description": "Basic search bar with toggle functionality."}, "variants": {"title": "Search Bar Variants", "description": "Search bars with different styles."}, "animation": {"title": "Search Bar Animation", "description": "Search bar with toggle animation."}, "withoutClear": {"title": "Search Bar without <PERSON>", "description": "Search bar without the clear button."}, "customWidth": {"title": "Search Bar with Custom Width", "description": "Search bar with custom maximum width."}, "show": "Show", "hide": "<PERSON>de"}, "modernMenu": {"title": "Modern Menu", "description": "Modern menu component with various styles and placements.", "basic": {"title": "Basic Menu", "description": "Basic menu with default settings."}, "withIcons": {"title": "<PERSON>u with Icons", "description": "Menu items with icons."}, "placement": {"title": "Menu Placement", "description": "Menu with different placements.", "top": "Top", "right": "Right", "bottom": "Bottom", "left": "Left"}}, "menu": {"title": "<PERSON><PERSON>", "description": "Menu with multiple features: submenu, different modes, collapsed state", "horizontal": {"title": "<PERSON><PERSON>u", "description": "Menu displayed horizontally"}, "vertical": {"title": "Vertical Menu", "description": "Menu displayed vertically"}, "inline": {"title": "Inline Menu", "description": "Menu with submenu displayed inline"}, "variants": {"title": "<PERSON><PERSON>", "description": "Different variants of Menu"}, "advanced": {"title": "Advanced Menu Items", "description": "Menu items with badge and shortcut"}, "expand": "Expand", "collapse": "Collapse"}, "showCode": "Show code", "hideCode": "Hide code", "copied": "Copied!", "copy": "Copy", "categories": {"buttons": {"title": "Buttons", "description": "Different types of buttons: primary, secondary, outline, icon buttons..."}, "cards": {"title": "Cards", "description": "Different types of cards for displaying content, information, data..."}, "chips": {"title": "Chips", "description": "Chips are compact elements that represent an input, attribute, or action..."}, "inputs": {"title": "Inputs", "description": "Different types of inputs: text, number, checkbox, radio, select..."}, "layout": {"title": "Layout Components", "description": "Layout components: container, grid, flex, resizer..."}, "theme": {"title": "Theme Components", "description": "Theme-related components: theme toggle, language selector...", "system": {"title": "Theme System", "description": "New theme system with customization and extension capabilities"}}, "typography": {"title": "Typography", "description": "Typography components for consistent text styling across your application."}, "form": {"title": "Form Components", "description": "Form components for building forms with validation, layouts, and more...", "theme": {"title": "Form with Theme System", "description": "Demo of form components using the new theme system"}}, "formDependencies": {"title": "Form Field Dependencies", "description": "Manage dependencies between form fields to create dynamic forms..."}}, "buttons": {"title": "Buttons", "description": "Different types of buttons used in the RedAI system.", "variants": {"title": "Button Variants", "description": "Button variants: primary, secondary, outline, success, warning, danger"}, "sizes": {"title": "Button Sizes", "description": "Button sizes: small, medium, large"}, "withIcons": {"title": "Button with Icons", "description": "Button with icons on the left or right"}, "fullWidth": {"title": "Full Width <PERSON>", "description": "Button that takes up the full width of its container", "button": "Full Width <PERSON>"}, "loading": {"title": "Loading <PERSON>ton", "description": "Button in loading state"}, "disabled": {"title": "Disabled <PERSON><PERSON>", "description": "<PERSON>ton in disabled state"}}, "grid": {"title": "Grid", "description": "Grid component helps create flexible and responsive grid layouts.", "basic": {"title": "Basic Grid", "description": "Grid with fixed number of columns"}, "responsive": {"title": "Responsive Grid", "description": "Grid with number of columns that change based on screen size"}, "gaps": {"title": "Grid Gaps", "description": "Grid with different spacing between elements", "small": "Small gap", "medium": "Medium gap", "large": "Large gap"}}, "calendar": {"title": "Calendar Components", "description": "Collection of calendar and event management components", "basic": {"title": "Basic Calendar", "description": "Basic calendar with event display, date selection, and interaction", "example": {"title": "Basic Calendar", "description": "Calendar with sample events, date selection, and event click"}, "usage": {"title": "Usage", "description": "How to use the Calendar component"}}, "eventForm": {"title": "Event Form", "description": "Form for adding and editing calendar events with various options", "example": {"title": "Event Form", "description": "Add/edit event form with basic and advanced fields"}, "usage": {"title": "Usage", "description": "How to use the EventForm component"}, "showForm": "Show event form", "addEvent": "Add event"}, "recurrence": {"title": "Recurrence Selector", "description": "Component for configuring event recurrence with various options", "basic": {"title": "Basic Recurrence Configuration", "description": "Recurrence configuration component with null initial value"}, "daily": {"title": "Daily Recurrence", "description": "Daily recurrence configuration component"}, "weekly": {"title": "Weekly Recurrence", "description": "Weekly recurrence configuration with days of the week"}, "usage": {"title": "Usage", "description": "How to use the RecurrenceSelector component"}, "label": "Repeat", "dailyLabel": "Daily repeat", "weeklyLabel": "Weekly repeat", "currentValue": "Current value"}, "reminders": {"title": "Reminder Selector", "description": "Component for configuring event reminders", "basic": {"title": "Basic Reminder Configuration", "description": "Reminder configuration component with default values"}, "empty": {"title": "Empty Reminder Configuration", "description": "Reminder configuration component with empty list"}, "disabled": {"title": "Disabled Re<PERSON>er Configuration", "description": "Reminder configuration component in disabled state"}, "usage": {"title": "Usage", "description": "How to use the ReminderSelector component"}, "label": "Reminders", "disabledLabel": "Reminders (Disabled)", "currentValue": "Current value"}, "userSelector": {"title": "User Selector", "description": "Component for selecting users for events", "basic": {"title": "Basic User Selection", "description": "Component for selecting a single user"}, "multiple": {"title": "Multiple User Selection", "description": "Component for selecting multiple users"}, "groups": {"title": "User Selection with Groups", "description": "Component for selecting users with user groups"}, "usage": {"title": "Usage", "description": "How to use the UserSelector component"}, "label": "Participants", "multipleLabel": "Participants", "groupsLabel": "Participants", "currentValue": "Current value"}, "fileUploader": {"title": "File Uploader", "description": "Component for uploading attachment files for events", "basic": {"title": "Basic File Upload", "description": "File upload component with sample files"}, "empty": {"title": "Empty File Upload", "description": "File upload component with empty list"}, "restricted": {"title": "File Upload with Type Restrictions", "description": "File upload component with file type restrictions"}, "usage": {"title": "Usage", "description": "How to use the FileUploader component"}, "label": "Attachments", "emptyLabel": "Attachments", "imageLabel": "Upload images", "currentValue": "Current value"}}, "banner": {"title": "Banner", "description": "Banner component displays prominent content with various options.", "basic": {"title": "Basic Banner", "description": "Basic banner with title and description."}, "withBackground": {"title": "Banner with Background", "description": "Banner with background image and overlay."}, "gradient": {"title": "Banner with <PERSON><PERSON><PERSON>", "description": "Banner with gradient background and action buttons."}, "wave": {"title": "Banner with Wave Effect", "description": "Banner with wave effect at the bottom."}, "custom": {"title": "Banner with Custom Content", "description": "Banner with custom content instead of using title and description."}}, "animation": {"title": "Animation", "description": "Various animations available in the RedAI Frontend Template.", "fadeSlide": {"title": "Fade & Slide Animations"}, "fadeIn": "Fade In", "fadeInAnimation": "Fade In Animation", "slideIn": "Slide In", "slideInAnimation": "Slide In Animation", "slideInLeft": "Slide In Left", "slideInLeftAnimation": "Slide In Left Animation", "slideInRight": "Slide In Right", "slideInRightAnimation": "Slide In Right Animation", "durationTiming": {"title": "Duration & Timing"}, "fast": "Fast (200ms)", "fastAnimation": "Fast Animation", "medium": "Medium (500ms)", "mediumAnimation": "Medium Animation", "slow": "Slow (1000ms)", "slowAnimation": "Slow Animation", "continuous": {"title": "Continuous Animations"}, "pulse": "Pulse", "pulseAnimation": "Pulse Animation", "spin": "Spin", "component": {"title": "Component Animations"}, "tooltipContent": "This is a tooltip with animation", "hoverMe": "Hover Me", "openModal": "Open Modal", "animatedModal": "Animated Modal", "modalDescription": "This modal has entrance and exit animations.", "infoNotification": "Info Notification", "successNotification": "Success Notification", "warningNotification": "Warning Notification", "errorNotification": "Error Notification", "notificationDescription": "This notification has entrance and exit animations."}, "responsiveGrid": {"title": "Responsive Grid", "description": "Advanced responsive grid that automatically adjusts based on screen size and chat panel state.", "basic": {"title": "Basic Responsive Grid", "description": "Responsive grid with default settings"}, "customColumns": {"title": "Custom Columns", "description": "Responsive grid with custom column settings"}, "customGaps": {"title": "Custom Gaps", "description": "Responsive grid with custom gap settings"}, "withChatPanel": {"title": "With Chat Panel", "description": "Responsive grid that adjusts when chat panel is opened or closed"}, "realWorld": {"title": "Real-world Example", "description": "Example of responsive grid used in the AI Agents module"}, "currentColumns": "Current columns", "chatPanel": "Chat panel", "open": "Open", "closed": "Closed", "openChatPanel": "Open Chat Panel", "closeChatPanel": "Close Chat Panel", "props": "Props", "propName": "Prop", "propType": "Type", "propDefault": "<PERSON><PERSON><PERSON>", "propDescription": "Description", "childrenDescription": "The child elements to display in the grid", "gapDescription": "The gap between grid items", "maxColumnsDescription": "Maximum columns for each breakpoint when chat panel is closed", "maxColumnsWithChatPanelDescription": "Maximum columns for each breakpoint when chat panel is open", "classNameDescription": "Additional CSS classes", "onColumnsChangeDescription": "Callback when columns change"}, "cards": {"title": "Cards", "description": "Different types of cards for displaying content, information, data in the RedAI system.", "basic": {"title": "Basic Card", "description": "Basic card with title and content"}, "withFooter": {"title": "Card with Footer", "description": "Card with a footer containing actions"}, "withIcon": {"title": "Card with Icon", "description": "Card with an icon in the header"}, "bordered": {"title": "Bordered Card", "description": "Card with a border around it"}, "customHeader": {"title": "Card with Custom Header", "description": "Card with a customized header"}}, "theme": {"title": "Theme Components", "description": "Theme-related components in the RedAI system.", "toggle": {"title": "Theme Toggle", "description": "Toggle switch between light mode and dark mode"}, "toggleSizes": {"title": "Theme Toggle Sizes", "description": "Theme Toggle with different sizes"}, "toggleCustomText": {"title": "Theme Toggle with Custom Text", "description": "Theme Toggle with customized text"}, "languageFlags": {"title": "Language Flags", "description": "Display flags for supported languages"}, "languageFlagSizes": {"title": "Language Flag Sizes", "description": "Language Flag with different sizes"}, "languageFlagWithLabel": {"title": "Language Flag with Label", "description": "Language Flag with language name label"}, "system": {"title": "Theme System", "description": "New theme system with customization and extension capabilities", "variables": {"title": "CSS Variables", "description": "CSS variables used in the theme system", "primaryColor": "Primary Color", "primaryDescription": "Using primary and primary-foreground colors", "secondaryColor": "Secondary Color", "secondaryDescription": "Using secondary and secondary-foreground colors", "accentColor": "Accent Color", "accentDescription": "Using accent and accent-foreground colors", "successColor": "Success Color", "successDescription": "Using success and success-foreground colors", "warningColor": "Warning Color", "warningDescription": "Using warning and warning-foreground colors", "errorColor": "Error Color", "errorDescription": "Using error and error-foreground colors"}, "cards": {"title": "Card Styles", "description": "Card styles using the new theme system", "content": "Card content using theme variables", "mutedTitle": "Muted Card", "mutedContent": "Using card-muted background"}, "typography": {"title": "Typography", "description": "Typography using the new theme system"}}, "customizer": {"title": "Theme Customizer", "description": "Component that allows users to customize the theme", "open": "Open Theme Customizer", "mode": "Theme Mode", "primaryColor": "Primary Color", "secondaryColor": "Secondary Color", "backgroundColor": "Background Color", "textColor": "Text Color", "borderRadius": "Border Radius"}}, "chips": {"title": "Chips", "description": "Chip components for displaying compact elements that represent an input, attribute, or action.", "basic": {"title": "Basic Chips", "description": "Basic chips with different variants."}, "outlined": {"title": "Outlined Chips", "description": "Outlined chips with different variants."}, "sizes": {"title": "<PERSON>", "description": "Chips in different sizes."}, "icons": {"title": "Chips with Icons", "description": "Chips with left and right icons."}, "closable": {"title": "Closable Chips", "description": "Chips with close button."}, "clickable": {"title": "Clickable Chips", "description": "Chips that can be clicked."}, "disabled": {"title": "Disabled Chips", "description": "Chips in disabled state."}, "avatar": {"title": "Chips with Avatar", "description": "Chips with avatar."}, "loading": {"title": "Loading Chips", "description": "Chips in loading state."}, "selected": {"title": "Selected Chips", "description": "Chips in selected state."}, "animation": {"title": "Animated Chips", "description": "Chips with animation effects."}, "group": {"title": "Chip Group", "description": "Group of chips with various features.", "basic": "Basic Chip Group", "closable": "Closable Chip Group", "multiSelect": "Multi-select Chip Group", "animated": "Animated Chip Group"}}, "typography": {"title": "Typography", "description": "Typography components for consistent text styling across your application.", "headings": {"title": "Headings", "description": "Heading variants from h1 to h6."}, "body": {"title": "Body Text", "description": "Body text variants for paragraphs and general content."}, "colors": {"title": "Text Colors", "description": "Typography with different color options."}, "weights": {"title": "Font Weights", "description": "Typography with different font weights."}, "alignment": {"title": "Text Alignment", "description": "Typography with different text alignments."}, "truncation": {"title": "Text Truncation", "description": "Typography with truncation for long text."}, "responsive": {"title": "Responsive Typography", "description": "Typography with responsive font sizes."}}, "inputs": {"title": "Inputs", "description": "Different types of inputs used in the RedAI system.", "select": {"title": "Select Component", "description": "Advanced select component with multiple features", "newFormat": {"title": "Select Component (New Format)", "description": "Select component with detailed documentation"}, "advanced": {"title": "Advanced Select Components", "description": "Async, Creatable, Combobox, and Typeahead Select components"}, "advancedNew": {"title": "Advanced Select Components (New Format)", "description": "Advanced Select components with detailed documentation"}, "usage": {"title": "Select Usage Examples", "description": "Practical examples of using Select components in forms"}}, "datepicker": {"title": "DatePicker Component", "description": "Advanced date picker with single date and range selection"}, "checkboxRadio": {"title": "Checkbox & Radio Components", "description": "Checkbox, CheckboxGroup, Radio, and RadioGroup components"}, "text": {"title": "Text Input", "description": "Basic input for text entry"}, "password": {"title": "Password Input", "description": "Input for password entry with visibility toggle"}, "helperText": {"title": "Input with Helper Text", "description": "Input with supporting text below"}, "error": {"title": "Input with Error", "description": "Input in error state"}, "disabled": {"title": "Disabled Input", "description": "Input in disabled state"}, "withIcon": {"title": "Input with Icon", "description": "Input with icon on the left or right"}, "labels": {"username": "Username", "password": "Password", "email": "Email", "search": "Search", "website": "Website"}, "placeholders": {"username": "Enter your username", "password": "Enter your password", "email": "Enter your email", "search": "Search...", "website": "Enter website URL"}, "helpers": {"emailPrivacy": "We'll never share your email with anyone else."}, "errors": {"invalidEmail": "Please enter a valid email address"}}, "layout": {"title": "Layout Components", "description": "Layout components used to organize content in the RedAI system.", "container": {"title": "Container", "description": "Container limits content width and centers it"}, "grid": {"title": "Grid", "description": "Grid layout with customizable columns"}, "responsiveGrid": {"title": "Responsive Grid", "description": "Grid layout with columns that change based on screen size"}, "resizer": {"title": "Resizer", "description": "Component that allows resizing between two panels"}, "horizontalResizer": {"title": "Horizontal Resizer", "description": "Resizer in horizontal orientation"}, "panels": {"left": "Left Panel", "right": "Right Panel", "top": "Top Panel", "bottom": "Bottom Panel"}}, "form": {"title": "Form Components", "description": "A collection of form components for building forms with various layouts and behaviors.", "basic": {"title": "Basic Form", "description": "A basic form with validation using Zod schema.", "example1": {"title": "Basic Form", "description": "A simple form with basic validation using Zod schema."}, "example2": {"title": "Form with Advanced Validation", "description": "A form with advanced validation rules using Zod schema."}, "fields": {"name": "Name", "email": "Email", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password"}, "placeholders": {"name": "Enter your name", "email": "Enter your email", "username": "Enter username", "password": "Enter password", "confirmPassword": "Confirm password"}, "helpTexts": {"agreeTerms": "I agree to the terms and conditions"}, "buttons": {"submit": "Submit", "register": "Register"}, "result": "Result", "reference": {"title": "Form Components Reference", "form": {"title": "Form Component", "description": "The main component that manages form state and validation."}, "formItem": {"title": "FormItem Component", "description": "Wraps input fields with labels, error messages, and help text."}, "input": {"title": "Input Component", "description": "Basic input component with various options."}}}, "dependencies": {"title": "Form Field Dependencies", "description": "Create dependent fields like cascading dropdowns"}, "templates": {"title": "Form Templates", "description": "Ready-to-use form templates for common use cases"}, "array": {"title": "Form Arrays", "description": "Dynamic form fields with add/remove functionality"}, "labels": {"name": "Name", "email": "Email", "country": "Country", "gender": "Gender", "agreeTerms": "I agree to the terms and conditions", "username": "Username", "password": "Password"}, "placeholders": {"name": "Enter your name", "email": "Enter your email", "country": "Select your country", "password": "Enter your password"}, "helpers": {"passwordLength": "Must be at least 8 characters"}, "errors": {"usernameTaken": "Username is already taken", "selectCountry": "Please select a country"}, "countries": {"us": "United States", "uk": "United Kingdom", "ca": "Canada", "au": "Australia", "de": "Germany", "fr": "France", "jp": "Japan", "cn": "China", "in": "India", "br": "Brazil"}, "gender": {"male": "Male", "female": "Female", "other": "Other"}, "grid": {"title": "Form Grid Layout", "description": "Organize form fields in a responsive grid layout."}, "inline": {"title": "Form Inline Layout", "description": "Create inline forms for search or simple inputs."}, "horizontal": {"title": "Form Horizontal Layout", "description": "Create forms with labels on the left and fields on the right."}, "layout": {"title": "Form Layouts", "description": "Different layout options for organizing form fields.", "grid": {"title": "Grid Layout", "description": "Organize form fields in a responsive grid layout."}, "inline": {"title": "Inline Layout", "description": "Create inline forms for search bars and filters."}, "horizontal": {"title": "Horizontal Layout", "description": "Create forms with labels on the left and fields on the right."}, "fields": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "address": "Address", "city": "City", "state": "State", "zipCode": "Zip Code", "search": "Search", "category": "Category", "username": "Username", "password": "Password"}, "placeholders": {"firstName": "Enter first name", "lastName": "Enter last name", "email": "Enter email", "phone": "Enter phone number", "address": "Enter address", "city": "Enter city", "state": "Enter state", "zipCode": "Enter zip code", "search": "Search...", "username": "Enter username", "password": "Enter password"}, "options": {"allCategories": "All Categories", "products": "Products", "services": "Services", "blogs": "Blogs"}, "buttons": {"submit": "Submit", "search": "Search", "login": "<PERSON><PERSON>"}, "result": "Result", "reference": {"title": "Form Layout Components Reference", "formGrid": {"title": "FormGrid Component", "description": "Creates a responsive grid layout for form fields."}, "formInline": {"title": "FormInline Component", "description": "Creates an inline layout for form fields."}, "formHorizontal": {"title": "FormHorizontal Component", "description": "Creates a horizontal layout with labels on the left and fields on the right."}}}, "conditional": {"title": "Conditional Fields", "description": "Show or hide fields based on conditions.", "basic": {"title": "Basic Conditional Fields", "description": "Show fields based on a simple condition."}, "advanced": {"title": "Advanced Conditional Fields", "description": "Use complex conditions with AND/OR logic."}, "component": {"title": "ConditionalField Component", "description": "The ConditionalField component allows you to show or hide form fields based on conditions. It's useful for creating dynamic forms that adapt to user input.", "basicUsage": {"title": "Basic Usage", "description": "The most common use case is to show fields based on a simple condition:"}, "conditionTypes": {"title": "Condition Types", "description": "The ConditionalField component supports various condition types:"}}, "complex": {"title": "Complex Conditions", "description": "The ConditionalField component supports complex conditions using AND and OR logic. This allows you to create sophisticated form behaviors.", "and": {"title": "AND Logic", "description": "Use AND logic when you want to show fields only when multiple conditions are met:"}, "or": {"title": "OR Logic", "description": "Use OR logic when you want to show fields when any of the conditions are met:"}}}, "apiForm": {"title": "API Form Integration", "description": "Integrate forms with API calls using the useApiForm hook."}, "apiError": {"title": "Form API Error Handling", "description": "Handle API errors in forms using the useFormErrors hook."}, "theme": {"title": "Form with Theme System", "description": "Demo of form components using the new theme system"}, "themeDemo": {"title": "Form Components with Theme System", "description": "Demo of form components using the new theme system"}, "basicForm": {"title": "Basic Form", "description": "A basic form with various input types"}, "examples": {"title": "Form Examples", "description": "Complete form examples for common use cases.", "login": {"title": "Login & Registration Forms", "description": "Complete examples of login and registration forms with validation."}, "multistep": {"title": "Multi-step Form", "description": "Example of a multi-step form with progress tracking and validation."}, "datepicker": {"title": "Advanced DatePicker Examples", "description": "Real-world examples of DatePicker usage including business days, holidays, and form integration."}, "checkboxRadio": {"title": "Checkbox & Radio Examples", "description": "Examples of Checkbox and Radio components with different states, sizes, and layouts."}}, "demo": {"title": "Form Examples", "description": "Complete examples of forms with validation using React Hook Form and Zod.", "result": "Result", "login": {"title": "Login Form", "description": "A complete login form with validation and error handling.", "emailHelp": "Enter your registered email"}, "register": {"title": "Registration Form", "description": "A complete registration form with advanced validation rules.", "passwordHelp": "Password must be at least 8 characters and include uppercase, lowercase, number, and special character", "confirmPasswordPlaceholder": "Confirm your password"}, "bestPractices": {"title": "Form Best Practices", "validation": {"title": "Form Validation", "description": "Always validate user input both on the client and server side.", "item1": "Use Zod for schema validation", "item2": "Provide clear error messages", "item3": "Validate in real-time when possible"}, "accessibility": {"title": "Accessibility", "description": "Ensure your forms are accessible to all users.", "item1": "Use proper labels for all form fields", "item2": "Ensure keyboard navigation works", "item3": "Use ARIA attributes when necessary"}, "ux": {"title": "User Experience", "description": "Create a smooth user experience.", "item1": "Show loading states during submission", "item2": "Disable the submit button when the form is invalid", "item3": "Provide clear success and error feedback"}}}, "validation": {"uppercase": "Password must contain at least 1 uppercase letter", "lowercase": "Password must contain at least 1 lowercase letter", "number": "Password must contain at least 1 number", "special": "Password must contain at least 1 special character", "agreeTerms": "You must agree to the terms and conditions"}, "variants": {"title": "Form Component Variants", "description": "Different variants of form components", "inputError": {"title": "Input with error"}, "inputHelper": {"title": "Input with helper text"}, "checkbox": {"title": "Checkbox variants", "default": "Default Checkbox", "rounded": "Rounded Checkbox", "filled": "Filled Checkbox", "outlined": "Outlined Checkbox"}, "radio": {"title": "Radio variants", "default": "Default Radio", "filled": "Filled Radio", "outlined": "Outlined Radio"}, "selectError": {"title": "Select with error"}}}, "formDependencies": {"title": "Form Field Dependencies", "description": "Manage dependencies between form fields to create dynamic forms.", "transform": {"title": "Transform Dependencies", "description": "Update field values based on other fields using transform functions."}, "cascadingSelect": {"title": "Cascading Select", "description": "Update select options based on the value of another select."}, "resetFields": {"title": "Reset Fields", "description": "Reset field values when another field changes."}}, "formSections": {"title": "Form Sections", "description": "Divide forms into sections for better organization and usability.", "collapsible": {"title": "Collapsible Sections", "description": "Sections that can be expanded or collapsed to save space and focus on the current task."}, "nestedSections": {"title": "Nested Sections", "description": "Nested sections for organizing complex forms."}, "customStyling": {"title": "Custom Styling", "description": "Customize section styling through className props."}, "variants": {"title": "Section Variants", "description": "Different variants of FormSection.", "default": "<PERSON><PERSON><PERSON>", "defaultDesc": "This is the default variant", "bordered": "Bordered", "borderedDesc": "This has a thicker border", "elevated": "Elevated", "elevatedDesc": "This has a shadow effect", "gradient": "Gradient", "gradientDesc": "This has a gradient background"}, "sizes": {"title": "Section Sizes", "description": "Different sizes of FormSection.", "small": "Small", "smallDesc": "This is a small section", "medium": "Medium", "mediumDesc": "This is a medium section (default)", "large": "Large", "largeDesc": "This is a large section"}, "animation": {"title": "Animation", "description": "Animation effects when opening/closing sections.", "fade": "Fade", "fadeDesc": "This section uses fade animation", "slide": "Slide", "slideDesc": "This section uses slide animation", "both": "Both", "bothDesc": "This section uses both fade and slide animations"}, "accordion": {"title": "Accordion", "description": "Only one section can be open at a time.", "group1": "Group 1", "group2": "Group 2", "firstSection": "First section in accordion", "secondSection": "Second section in accordion", "thirdSection": "Third section in accordion"}, "badge": {"title": "Badge", "description": "Display badge next to the title.", "new": "New", "required": "Required", "optional": "Optional", "stringBadgeTitle": "Section with String Badge", "stringBadgeDesc": "This section has a string badge", "customBadgeTitle": "Section with Custom Badge", "customBadgeDesc": "This section has a custom badge component"}, "iconPosition": {"title": "Icon Position", "description": "Position of the icon in the header.", "left": "Left", "leftDesc": "Icon on the left", "right": "Right", "rightDesc": "Icon on the right (default)"}, "fields": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone Number", "companyName": "Company Name", "jobTitle": "Job Title", "department": "Department", "address": "Address", "city": "City", "state": "State", "zipCode": "Zip Code", "country": "Country", "cardNumber": "Card Number", "cardName": "Cardholder Name", "cardExpiry": "Expiry Date", "cardCvv": "CVV", "saveCard": "Save Card Information", "receiveNewsletter": "Receive Newsletter"}, "placeholders": {"firstName": "Enter your first name", "lastName": "Enter your last name", "email": "<EMAIL>", "phone": "Enter your phone number", "companyName": "Enter your company name", "jobTitle": "Enter your job title", "department": "Enter your department"}, "buttons": {"submit": "Submit"}, "result": "Result", "personalInfo": "Personal Information", "personalInfoDesc": "Enter your personal details", "companyInfo": "Company Information", "companyInfoDesc": "Enter your company information (if applicable)", "formSectionComponent": "FormSection Component", "formSectionDesc": "The FormSection component is used to group form fields into sections, helping to organize forms in a clear and usable way.", "features": "Features", "featuresList": {"groupFields": "Group form fields into sections", "collapsible": "Support for collapsible sections (can be opened/closed)", "titleDesc": "Display title and description for the section", "variants": "Multiple variants: default, bordered, elevated, gradient", "sizes": "Multiple sizes: sm, md, lg", "animation": "Animation effects when opening/closing: fade, slide, both", "accordion": "Support for accordion mode (only one section open at a time)", "badge": "Support for badge next to the title", "iconPosition": "Customizable icon position: left, right", "customStyle": "Customize styling through className", "darkMode": "Full support for light/dark mode", "responsive": "Responsive on all screen sizes"}, "props": "Props", "propsList": {"title": "Title of the section", "description": "Description of the section (optional)", "collapsible": "Whether the section can be opened/closed (default: false)", "defaultExpanded": "Default state of the section (default: true)", "variant": "Variant of the section (default, bordered, elevated, gradient)", "size": "Size of the section (sm, md, lg)", "icon": "Custom icon for the header", "iconPosition": "Position of the icon (left, right)", "badge": "Badge displayed next to the title", "animated": "Whether to use animation when opening/closing", "animationType": "Type of animation (fade, slide, both)", "animationDuration": "Duration of the animation (ms)", "accordionId": "ID of the accordion group", "id": "ID of the section (used for accordion)", "onExpandChange": "Callback when open/close state changes", "className": "Additional class for the section", "titleClassName": "Additional class for the title", "descriptionClassName": "Additional class for the description", "contentClassName": "Additional class for the content", "headerClassName": "Additional class for the header"}, "collapsibleSections": "Collapsible Sections", "collapsibleDesc": "FormSection supports collapsible functionality to save space and help users focus on the current task.", "usage": "Usage", "usageSteps": {"addProp": "Add the collapsible prop to FormSection", "defaultState": "Use defaultExpanded to set the default state", "trackChanges": "Use onExpandChange to track state changes"}, "managingSections": "Managing Multiple Sections", "managingSectionsDesc": "To manage the open/close state of multiple sections, you can:", "managingStepsList": {"stateObject": "Store the state in a state object", "functions": "Create functions to open/close all sections", "callback": "Use the onExpandChange callback to update state"}, "gridIntegration": "Integration with FormGrid", "gridIntegrationDesc": "FormSection can be combined with FormGrid to create more complex layouts for forms.", "advantages": "Advantages", "advantagesList": {"gridLayout": "Organize fields in a grid layout", "responsive": "Responsive on different screen sizes", "columns": "Easily adjust the number of columns and spacing"}, "gridUsage": "Usage", "gridUsageSteps": {"placeGrid": "Place FormGrid inside FormSection", "setColumns": "Set the number of columns and spacing", "spanColumns": "Use className=\"col-span-2\" to make a field span multiple columns"}, "customStylingTitle": "Custom Styling", "customStylingDesc": "FormSection provides multiple props to customize the styling of different parts of the section.", "stylingProps": "<PERSON><PERSON><PERSON> Props", "stylingPropsList": {"className": "Customize the styling of the entire section", "titleClassName": "Customize the styling of the title", "descriptionClassName": "Customize the styling of the description", "contentClassName": "Customize the styling of the content"}, "examples": "Examples", "examplesList": {"primaryBorder": "Add primary color border: className=\"border-2 border-primary\"", "titleColor": "Change title color: titleClassName=\"text-primary\"", "italicDesc": "Italic description: descriptionClassName=\"italic\"", "contentBg": "Change content background: contentClassName=\"bg-gray-50\""}, "variantsTitle": "Variants", "variantsDesc": "FormSection supports multiple variants to match your application's design.", "variantsList": {"default": "Default variant with thin border", "bordered": "Variant with thicker border", "elevated": "Variant with shadow effect", "gradient": "Variant with gradient background"}, "variantUsage": "Usage", "badgeAndIcon": "Badge and Icon Position", "badgeDesc": "FormSection supports displaying a badge next to the title to indicate status or provide additional information.", "badgeFeatures": {"stringNumber": "Badge can be a string, number, or ReactNode", "defaultBadge": "Strings and numbers will be displayed in the default Badge component", "customBadge": "ReactNode allows for fully custom badges"}, "iconPositionDesc": "FormSection allows customizing the position of the open/close icon.", "iconPositions": {"right": "Right side (default)", "left": "Left side"}, "expandAll": "Expand All", "collapseAll": "Collapse All", "newsletterSubscription": "Newsletter Subscription", "managePreferences": "Manage your newsletter preferences", "sizesDesc": "FormSection supports multiple sizes to match your application's design.", "sizesTitle": "Sizes", "sizesList": {"small": "Small size, suitable for compact forms", "medium": "Medium size (default)", "large": "Large size, suitable for forms that need emphasis"}, "sizeUsage": "Use the size prop to select a size", "animationDesc": "FormSection supports multiple animation effects when opening/closing sections.", "animationTypes": "Animation Types", "animationTypesList": {"fade": "Fade effect when closing/opening", "slide": "Slide up/down effect when closing/opening", "both": "Combination of both fade and slide effects"}, "animationUsage": "Use the animated and animationType props", "accordionDesc": "FormSection supports accordion mode, allowing only one section to be open at a time.", "howItWorks": "How It Works", "accordionWorkingList": {"oneAtTime": "Sections in the same accordion group will close when another section is opened", "uniqueId": "Each section needs a unique ID", "sameAccordionId": "Sections in the same group need the same accordionId"}, "accordionUsage": "Use the accordionId and id props"}}}