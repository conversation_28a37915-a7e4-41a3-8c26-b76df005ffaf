import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import affiliateResources from '@/modules/admin/affiliate/locales';
// Import Kanban locales
import { productResources } from '@/modules/product';
import { rpointAdminResources } from '@/modules/rpoint/admin';

import enTranslation from '../locales/en.json';
import { validationResources } from '../locales/validation';
import viTranslation from '../locales/vi.json';
import zhTranslation from '../locales/zh.json';
import authResources from '../modules/auth/locales';
import calendarResources from '../modules/calendar/locales';
import componentsResources from '../modules/components/locales';
import crmResources from '../modules/crm/locales';
import genericPageResources from '../modules/generic-page/locales';
import homeResources from '../modules/home/<USER>';
import hrmResources from '../modules/hrm/locales';
import marketingResources from '../modules/marketing/locales';
import okrsResources from '../modules/okrs/locales';
import profileResources from '../modules/profile/locales';
import { subscriptionResources } from '../modules/subscription/locales';
import todolistResources from '../modules/todolist/locales';
import kanbanEnTranslation from '../shared/components/kanban/locales/en.json';
import kanbanViTranslation from '../shared/components/kanban/locales/vi.json';
import kanbanZhTranslation from '../shared/components/kanban/locales/zh.json';

import adminEnTranslation from './i18n/locales/en/admin.json';
import integrationsEnTranslation from './i18n/locales/en/integrations.json';
import paymentEnTranslation from './i18n/locales/en/payment.json';
import workflowsEnTranslation from './i18n/locales/en/workflows.json';
import adminViTranslation from './i18n/locales/vi/admin.json';
import integrationsViTranslation from './i18n/locales/vi/integrations.json';
import paymentViTranslation from './i18n/locales/vi/payment.json';
import workflowsViTranslation from './i18n/locales/vi/workflows.json';
import adminZhTranslation from './i18n/locales/zh/admin.json';
import integrationsZhTranslation from './i18n/locales/zh/integrations.json';
import paymentZhTranslation from './i18n/locales/zh/payment.json';
import workflowsZhTranslation from './i18n/locales/zh/workflows.json';

export const resources = {
  en: {
    translation: enTranslation,
    auth: authResources.en.auth,
    profile: profileResources.en.profile,
    validation: validationResources.en.validation,
    components: componentsResources.en.components,
    calendar: calendarResources.en.calendar,
    home: homeResources.en.home,
    todolist: todolistResources.en.todolist,
    okrs: okrsResources.en.okrs,
    marketing: marketingResources.en.marketing,
    crm: crmResources.en.crm,
    hrm: hrmResources.en.hrm,
    subscription: subscriptionResources.en.subscription,
    genericPage: genericPageResources.en.genericPage,
    rpoint: rpointAdminResources.en.rpoint,
    affiliate: affiliateResources.en.affiliate,
    product: productResources.en.product,
    admin: adminEnTranslation,
    workflows: workflowsEnTranslation,
    integrations: integrationsEnTranslation,
    kanban: kanbanEnTranslation,
    payment: paymentEnTranslation,
  },
  vi: {
    translation: viTranslation,
    auth: authResources.vi.auth,
    profile: profileResources.vi.profile,
    validation: validationResources.vi.validation,
    components: componentsResources.vi.components,
    calendar: calendarResources.vi.calendar,
    home: homeResources.vi.home,
    todolist: todolistResources.vi.todolist,
    okrs: okrsResources.vi.okrs,
    marketing: marketingResources.vi.marketing,
    crm: crmResources.vi.crm,
    hrm: hrmResources.vi.hrm,
    subscription: subscriptionResources.vi.subscription,
    genericPage: genericPageResources.vi.genericPage,
    rpoint: rpointAdminResources.vi.rpoint,
    affiliate: affiliateResources.vi.affiliate,
    product: productResources.vi.product,
    admin: adminViTranslation,
    workflows: workflowsViTranslation,
    integrations: integrationsViTranslation,
    kanban: kanbanViTranslation,
    payment: paymentViTranslation,
  },
  zh: {
    translation: zhTranslation,
    auth: authResources.zh.auth,
    profile: profileResources.zh.profile,
    validation: validationResources.zh.validation,
    components: componentsResources.zh.components,
    calendar: calendarResources.zh.calendar,
    marketing: marketingResources.zh.marketing,
    subscription: subscriptionResources.zh.subscription,
    genericPage: genericPageResources.zh.genericPage,
    rpoint: rpointAdminResources.zh.rpoint,
    affiliate: affiliateResources.zh.affiliate,
    product: productResources.zh.product,
    admin: adminZhTranslation,
    workflows: workflowsZhTranslation,
    integrations: integrationsZhTranslation,
    kanban: kanbanZhTranslation,
    payment: paymentZhTranslation,
  },
};

export const availableLanguages = [
  { code: 'vi', name: 'Tiếng Việt' },
  { code: 'en', name: 'English' },
  { code: 'zh', name: '中文' },
];

i18n.use(initReactI18next).init({
  resources,
  lng: 'vi', // Default language
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false, // React already escapes values
  },
});

export default i18n;
