import { LanguageFlag } from '@/shared/components/common';
import { useLanguage } from '@/shared/contexts';
import React from 'react';
import LanguageDropdown from './LanguageDropdown';

interface LanguageSwitcherProps {
  /**
   * Variant of the language switcher
   */
  variant?: 'default' | 'minimal' | 'dropdown';
}

/**
 * Component for switching between languages
 */
const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ variant = 'default' }) => {
  const { language, setLanguage, availableLanguages } = useLanguage();

  if (variant === 'dropdown') {
    // Use custom dropdown component for better positioning
    return <LanguageDropdown />;
  }

  return (
    <div className="flex items-center space-x-2">
      {variant === 'default' ? (
        // Default variant with all languages
        availableLanguages.map(lang => (
          <LanguageFlag
            key={lang.code}
            code={lang.code as 'vi' | 'en' | 'zh'}
            isSelected={language === lang.code}
            onClick={() => setLanguage(lang.code as 'vi' | 'en' | 'zh')}
          />
        ))
      ) : (
        // Minimal variant with only current language
        <LanguageFlag
          code={language as 'vi' | 'en' | 'zh'}
          isSelected={true}
          onClick={() => {
            // Cycle through languages
            const currentIndex = availableLanguages.findIndex(lang => lang.code === language);
            const nextIndex = (currentIndex + 1) % availableLanguages.length;
            setLanguage(availableLanguages[nextIndex].code as 'vi' | 'en' | 'zh');
          }}
        />
      )}
    </div>
  );
};

export default LanguageSwitcher;
