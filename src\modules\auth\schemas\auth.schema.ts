import { TFunction } from 'i18next';
import { z } from 'zod';

/**
 * Login form values interface
 */
export interface LoginFormValues {
  username: string;
  password: string;
  rememberMe: boolean;
  recaptchaToken?: string;
}

/**
 * Register form values interface
 */
export interface RegisterFormValues {
  fullName: string;
  email: string;
  phone: string;
  password: string;
  recaptchaToken?: string;
}

/**
 * Company register form values interface
 */
export interface CompanyRegisterFormValues {
  companyName: string;
  taxCode: string;
  companyEmail: string;
  password: string;
  confirmPassword: string;
  phoneNumber: string;
  address: string;
  recaptchaToken?: string;
}

/**
 * Forgot password form values interface
 */
export interface ForgotPasswordFormValues {
  emailOrPhone: string;
}

/**
 * Create login schema with translations
 */
export const createLoginSchema = (t: TFunction) => {
  return z.object({
    username: z
      .string()
      .min(1, t('validation:email.required'))
      .email(t('validation:email.invalid')),
    password: z.string().min(6, t('validation:password.minLogin')),
    rememberMe: z.boolean().optional(),
    recaptchaToken: z.string().optional().nullable(),
  });
};

/**
 * Create register schema with translations
 */
export const createRegisterSchema = (t: TFunction) => {
  return z.object({
    fullName: z.string().min(2, t('validation:fullName.min')),
    email: z.string().min(1, t('validation:email.required')).email(t('validation:email.invalid')),
    phone: z
      .string()
      .min(10, t('validation:phone.invalid'))
      .max(11, t('validation:phone.invalid'))
      .regex(/^\d{10,11}$/, t('validation:phone.invalid')),
    password: z
      .string()
      .min(8, t('validation:password.min'))
      .regex(/[A-Z]/, t('validation:password.uppercase'))
      .regex(/[a-z]/, t('validation:password.lowercase'))
      .regex(/\d/, t('validation:password.number'))
      .regex(/[^\dA-Za-z]/, t('validation:password.special')),
    recaptchaToken: z.string().optional(),
  });
};

/**
 * Create forgot password schema with translations
 */
export const createForgotPasswordSchema = (t: TFunction) => {
  return z.object({
    email: z.string().min(1, t('validation:email.required')).email(t('validation:email.invalid')),
  });
};

/**
 * Reset password form values interface
 */
export interface ResetPasswordFormValues {
  password: string;
  confirmPassword: string;
}

/**
 * Create reset password schema with translations
 */
export const createResetPasswordSchema = (t: TFunction) => {
  return z
    .object({
      password: z
        .string()
        .min(8, t('validation:password.min'))
        .regex(/[A-Z]/, t('validation:password.uppercase'))
        .regex(/[a-z]/, t('validation:password.lowercase'))
        .regex(/\d/, t('validation:password.number'))
        .regex(/[^\dA-Za-z]/, t('validation:password.special')),
      confirmPassword: z.string().min(1, t('validation:confirmPassword.required')),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: t('validation:confirmPassword.match'),
      path: ['confirmPassword'],
    });
};

/**
 * Create company register schema with translations
 */
export const createCompanyRegisterSchema = (t: TFunction) => {
  return z
    .object({
      companyName: z
        .string()
        .min(3, t('validation:companyName.min'))
        .max(255, t('validation:companyName.max')),
      companyEmail: z
        .string()
        .min(1, t('validation:email.required'))
        .email(t('validation:email.invalid')),
      password: z
        .string()
        .min(8, t('validation:password.min'))
        .regex(/[A-Z]/, t('validation:password.uppercase'))
        .regex(/[a-z]/, t('validation:password.lowercase'))
        .regex(/\d/, t('validation:password.number'))
        .regex(/[^\dA-Za-z]/, t('validation:password.special')),
      confirmPassword: z.string().min(1, t('validation:confirmPassword.required')),
      phoneNumber: z
        .string()
        .min(10, t('validation:phone.invalid'))
        .max(15, t('validation:phone.invalid'))
        .regex(/^\d{10,15}$/, t('validation:phone.invalid')),
      recaptchaToken: z.string().optional(),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: t('validation:confirmPassword.match'),
      path: ['confirmPassword'],
    });
};
