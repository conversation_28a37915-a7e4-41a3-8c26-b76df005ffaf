import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import Calendar from '@/modules/calendar/components/Calendar';
import { CalendarEvent } from '@/modules/calendar/types';
import { Card, Typography } from '@/shared/components/common';
import { DateSelectArg, EventClickArg } from '@fullcalendar/core';

import { ComponentDemo } from '../../components';

/**
 * Trang demo cho Calendar cơ bản
 */
const BasicCalendarPage: React.FC = () => {
  const { t } = useTranslation();
  const [events] = useState<CalendarEvent[]>([
    {
      id: '1',
      title: 'Họp nhóm',
      start: new Date(new Date().setHours(10, 0, 0, 0)),
      end: new Date(new Date().setHours(12, 0, 0, 0)),
      description: '<PERSON>uộ<PERSON> họp nhóm hàng tuần để thảo luận về tiến độ dự án',
      location: 'Phòng họp A',
      className: 'calendar-event-meeting',
      extendedProps: {
        type: 'meeting',
      },
    },
    {
      id: '2',
      title: 'Ăn trưa với khách hàng',
      start: (() => {
        const date = new Date();
        date.setDate(date.getDate() + 1);
        date.setHours(12, 30, 0, 0);
        return date;
      })(),
      end: (() => {
        const date = new Date();
        date.setDate(date.getDate() + 1);
        date.setHours(13, 30, 0, 0);
        return date;
      })(),
      description: 'Ăn trưa với khách hàng để thảo luận về hợp đồng mới',
      location: 'Nhà hàng XYZ',
      className: 'calendar-event-lunch',
      extendedProps: {
        type: 'lunch',
      },
    },
    {
      id: '3',
      title: 'Deadline dự án',
      start: new Date(new Date().setDate(new Date().getDate() + 3)),
      allDay: true,
      description: 'Hạn chót nộp báo cáo dự án',
      className: 'calendar-event-deadline',
      extendedProps: {
        type: 'deadline',
      },
    },
  ]);

  // Xử lý khi chọn ngày trên lịch
  const handleDateSelect = (selectInfo: DateSelectArg) => {
    alert(`Đã chọn: ${selectInfo.startStr} đến ${selectInfo.endStr}`);
  };

  // Xử lý khi click vào sự kiện
  const handleEventClick = (clickInfo: EventClickArg) => {
    alert(`Đã click vào sự kiện: ${clickInfo.event.title}`);
  };

  return (
    <div className="p-4 sm:p-6 space-y-8">
      <div>
        <h1 className="text-2xl font-semibold mb-2 text-foreground">
          {t('components.calendar.basic.title', 'Basic Calendar')}
        </h1>
        <p className="text-muted">
          {t(
            'components.calendar.basic.description',
            'Calendar cơ bản với các tính năng hiển thị sự kiện, chọn ngày và tương tác'
          )}
        </p>
      </div>

      <ComponentDemo
        title={t('components.calendar.basic.example.title', 'Calendar cơ bản')}
        description={t(
          'components.calendar.basic.example.description',
          'Calendar với các sự kiện mẫu, có thể chọn ngày và click vào sự kiện'
        )}
        code={`import Calendar from '@/modules/calendar/components/Calendar';
import { CalendarEvent } from '@/modules/calendar/types';
import { DateSelectArg, EventClickArg } from '@fullcalendar/core';

const MyCalendar = () => {
  const [events, setEvents] = useState<CalendarEvent[]>([
    {
      id: '1',
      title: 'Họp nhóm',
      start: new Date(new Date().setHours(10, 0, 0, 0)),
      end: new Date(new Date().setHours(12, 0, 0, 0)),
      className: 'calendar-event-meeting',
      extendedProps: { type: 'meeting' }
    },
    {
      id: '2',
      title: 'Ăn trưa với khách hàng',
      start: new Date(new Date().setDate(new Date().getDate() + 1)).setHours(12, 30, 0, 0),
      end: new Date(new Date().setDate(new Date().getDate() + 1)).setHours(13, 30, 0, 0),
      className: 'calendar-event-lunch',
      extendedProps: { type: 'lunch' }
    },
    {
      id: '3',
      title: 'Deadline dự án',
      start: new Date(new Date().setDate(new Date().getDate() + 3)),
      allDay: true,
      className: 'calendar-event-deadline',
      extendedProps: { type: 'deadline' }
    }
  ]);

  // Xử lý khi chọn ngày trên lịch
  const handleDateSelect = (selectInfo: DateSelectArg) => {
    alert(\`Đã chọn: \${selectInfo.startStr} đến \${selectInfo.endStr}\`);
  };

  // Xử lý khi click vào sự kiện
  const handleEventClick = (clickInfo: EventClickArg) => {
    alert(\`Đã click vào sự kiện: \${clickInfo.event.title}\`);
  };

  return (
    <Calendar
      events={events}
      editable={true}
      selectable={true}
      onDateSelect={handleDateSelect}
      onEventClick={handleEventClick}
      height="600px"
    />
  );
};`}
      >
        <Card className="shadow-md rounded-lg overflow-hidden">
          <div className="flex justify-between items-center mb-4">
            <Typography variant="h5">
              {t('components.calendar.basic.example.title', 'Calendar cơ bản')}
            </Typography>
          </div>

          <Calendar
            events={events}
            editable={true}
            selectable={true}
            onDateSelect={handleDateSelect}
            onEventClick={handleEventClick}
            height="600px"
          />
        </Card>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.calendar.basic.usage.title', 'Cách sử dụng')}
        description={t(
          'components.calendar.basic.usage.description',
          'Hướng dẫn cách sử dụng component Calendar'
        )}
        code={`// Import component
import Calendar from '@/modules/calendar/components/Calendar';

// Sử dụng component
<Calendar
  events={events}                // Danh sách sự kiện
  initialView="dayGridMonth"     // Chế độ xem ban đầu (month, week, day, list)
  initialDate={new Date()}       // Ngày ban đầu
  editable={true}                // Cho phép kéo thả sự kiện
  selectable={true}              // Cho phép chọn ngày
  weekends={true}                // Hiển thị ngày cuối tuần
  allDaySlot={false}             // Hiển thị slot cả ngày
  height="auto"                  // Chiều cao của calendar
  onDateSelect={handleDateSelect}  // Xử lý khi chọn ngày
  onEventClick={handleEventClick}  // Xử lý khi click vào sự kiện
  onEventChange={handleEventChange} // Xử lý khi thay đổi sự kiện
/>`}
      >
        <div className="space-y-4">
          <Typography variant="h6">Props</Typography>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Prop
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Default
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Description
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    events
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    CalendarEvent[]
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    []
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Danh sách sự kiện
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    initialView
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    string
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    'dayGridMonth'
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Chế độ xem ban đầu
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    editable
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    boolean
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    true
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Cho phép kéo thả sự kiện
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    selectable
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    boolean
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    true
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Cho phép chọn ngày
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default BasicCalendarPage;
