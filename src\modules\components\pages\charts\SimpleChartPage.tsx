import React from 'react';

import { DirectLineChart, NewLineChart } from '@/shared/components/charts';
import { Container, Typography, Card } from '@/shared/components/common';

const SimpleChartPage: React.FC = () => {
  // D<PERSON> liệu mẫu cho biểu đồ
  const data = [
    { name: 'Page A', uv: 4000, pv: 2400, amt: 2400 },
    { name: 'Page B', uv: 3000, pv: 1398, amt: 2210 },
    { name: 'Page C', uv: 2000, pv: 9800, amt: 2290 },
    { name: 'Page D', uv: 2780, pv: 3908, amt: 2000 },
    { name: '<PERSON> E', uv: 1890, pv: 4800, amt: 2181 },
    { name: 'Page F', uv: 2390, pv: 3800, amt: 2500 },
    { name: 'Page G', uv: 3490, pv: 4300, amt: 2100 },
  ];

  // Cấu hình đường cho biểu đồ
  const lines = [
    { dataKey: 'pv', color: '#8884d8', name: 'Page Views' },
    { dataKey: 'uv', color: '#82ca9d', name: 'Unique Visitors' },
  ];

  return (
    <Container>
      <Typography variant="h4" className="mb-6">
        Simple Chart Demo
      </Typography>

      <Typography variant="body1" className="mb-6">
        Biểu đồ đường đơn giản sử dụng trực tiếp thư viện Recharts.
      </Typography>

      <div className="space-y-8">
        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            Direct Line Chart
          </Typography>
          <div style={{ height: 400, width: '100%' }}>
            <DirectLineChart />
          </div>
        </Card>

        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            New Line Chart
          </Typography>
          <div style={{ height: 400, width: '100%' }}>
            <NewLineChart data={data} lines={lines} xAxisKey="name" />
          </div>
        </Card>
      </div>
    </Container>
  );
};

export default SimpleChartPage;
