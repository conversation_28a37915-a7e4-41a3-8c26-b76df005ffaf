import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import { Card, Icon, ResponsiveGrid } from '@/shared/components/common';
import { IconName } from '@/shared/components/common/Icon/Icon';

interface ComponentCategory {
  titleKey: string;
  descriptionKey: string;
  path: string;
  iconName: IconName;
}

/**
 * Trang hiển thị danh sách các component Calendar
 */
const CalendarComponentsPage: React.FC = () => {
  const { t } = useTranslation();

  const categories: ComponentCategory[] = [
    {
      titleKey: 'components.calendar.basic.title',
      descriptionKey: 'components.calendar.basic.description',
      path: '/components/calendar/basic',
      iconName: 'calendar',
    },
    {
      titleKey: 'components.calendar.eventForm.title',
      descriptionKey: 'components.calendar.eventForm.description',
      path: '/components/calendar/event-form',
      iconName: 'edit',
    },
    {
      titleKey: 'components.calendar.recurrence.title',
      descriptionKey: 'components.calendar.recurrence.description',
      path: '/components/calendar/recurrence',
      iconName: 'calendar',
    },
    {
      titleKey: 'components.calendar.reminders.title',
      descriptionKey: 'components.calendar.reminders.description',
      path: '/components/calendar/reminders',
      iconName: 'info',
    },
    {
      titleKey: 'components.calendar.userSelector.title',
      descriptionKey: 'components.calendar.userSelector.description',
      path: '/components/calendar/user-selector',
      iconName: 'user',
    },
    {
      titleKey: 'components.calendar.fileUploader.title',
      descriptionKey: 'components.calendar.fileUploader.description',
      path: '/components/calendar/file-uploader',
      iconName: 'paperclip',
    },
  ];

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.calendar.title', 'Calendar Components')}
        </h1>
        <p className="text-muted">
          {t('components.calendar.description', 'Bộ sưu tập các component lịch và quản lý sự kiện')}
        </p>
      </div>

      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
        gap={{ xs: 4, md: 5, lg: 6 }}
      >
        {categories.map(category => (
          <Link key={category.path} to={category.path} className="block h-full">
            <Card
              variant="elevated"
              hoverable
              className="h-full transition-all duration-300 rounded-xl"
            >
              <div className="flex items-start p-6">
                <div className="mr-4 text-primary">
                  <Icon name={category.iconName} size="xl" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-foreground mb-2">
                    {t(category.titleKey)}
                  </h3>
                  <p className="text-sm text-muted">{t(category.descriptionKey)}</p>
                </div>
              </div>
            </Card>
          </Link>
        ))}
      </ResponsiveGrid>
    </div>
  );
};

export default CalendarComponentsPage;
