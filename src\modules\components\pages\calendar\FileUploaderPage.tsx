import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import FileUploader from '@/modules/calendar/components/files/FileUploader';
import {
  AttachmentFile,
  UploadStatus,
  ACCEPTED_FILE_TYPES,
} from '@/modules/calendar/components/files/types';
import { Card, Typography } from '@/shared/components/common';

import { ComponentDemo } from '../../components';

/**
 * Trang demo cho FileUploader
 */
const FileUploaderPage: React.FC = () => {
  const { t } = useTranslation();
  const [files, setFiles] = useState<AttachmentFile[]>([
    {
      id: '1',
      name: '<PERSON>à<PERSON> liệu cuộc họp.pdf',
      type: 'application/pdf',
      size: 1024 * 1024,
      status: UploadStatus.SUCCESS,
      url: '#',
    },
    {
      id: '2',
      name: '<PERSON><PERSON><PERSON><PERSON> sự kiện.jpg',
      type: 'image/jpeg',
      size: 2 * 1024 * 1024,
      status: UploadStatus.SUCCESS,
      url: '#',
    },
  ]);
  const [emptyFiles, setEmptyFiles] = useState<AttachmentFile[]>([]);

  // Giả lập hàm upload file
  const handleUpload = async (file: File): Promise<string> => {
    // Giả lập việc tải lên file
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(`https://example.com/files/${file.name}`);
      }, 2000);
    });
  };

  return (
    <div className="p-4 sm:p-6 space-y-8">
      <div>
        <h1 className="text-2xl font-semibold mb-2 text-foreground">
          {t('components.calendar.fileUploader.title', 'File Uploader')}
        </h1>
        <p className="text-muted">
          {t(
            'components.calendar.fileUploader.description',
            'Component tải lên tệp đính kèm cho sự kiện'
          )}
        </p>
      </div>

      <ComponentDemo
        title={t('components.calendar.fileUploader.basic.title', 'Tải lên tệp cơ bản')}
        description={t(
          'components.calendar.fileUploader.basic.description',
          'Component tải lên tệp với các tệp mẫu'
        )}
        code={`import FileUploader from '@/modules/calendar/components/files/FileUploader';
import { AttachmentFile, UploadStatus } from '@/modules/calendar/components/files/types';

const [files, setFiles] = useState<AttachmentFile[]>([
  {
    id: '1',
    name: 'Tài liệu cuộc họp.pdf',
    type: 'application/pdf',
    size: 1024 * 1024,
    status: UploadStatus.SUCCESS,
    url: '#'
  },
  {
    id: '2',
    name: 'Hình ảnh sự kiện.jpg',
    type: 'image/jpeg',
    size: 2 * 1024 * 1024,
    status: UploadStatus.SUCCESS,
    url: '#'
  }
]);

// Giả lập hàm upload file
const handleUpload = async (file: File): Promise<string> => {
  // Giả lập việc tải lên file
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(\`https://example.com/files/\${file.name}\`);
    }, 2000);
  });
};

<FileUploader
  files={files}
  onChange={setFiles}
  label="Tệp đính kèm"
  onUpload={handleUpload}
/>`}
      >
        <Card className="p-6">
          <FileUploader
            files={files}
            onChange={setFiles}
            label={t('components.calendar.fileUploader.label', 'Tệp đính kèm')}
            onUpload={handleUpload}
          />

          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
            <Typography variant="h6" className="mb-2">
              {t('components.calendar.fileUploader.currentValue', 'Giá trị hiện tại')}:
            </Typography>
            <pre className="text-sm overflow-auto p-2 bg-gray-100 dark:bg-gray-900 rounded">
              {JSON.stringify(files, null, 2)}
            </pre>
          </div>
        </Card>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.calendar.fileUploader.empty.title', 'Tải lên tệp trống')}
        description={t(
          'components.calendar.fileUploader.empty.description',
          'Component tải lên tệp với danh sách trống'
        )}
        code={`import FileUploader from '@/modules/calendar/components/files/FileUploader';
import { AttachmentFile } from '@/modules/calendar/components/files/types';

const [emptyFiles, setEmptyFiles] = useState<AttachmentFile[]>([]);

// Giả lập hàm upload file
const handleUpload = async (file: File): Promise<string> => {
  // Giả lập việc tải lên file
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(\`https://example.com/files/\${file.name}\`);
    }, 2000);
  });
};

<FileUploader
  files={emptyFiles}
  onChange={setEmptyFiles}
  label="Tệp đính kèm"
  onUpload={handleUpload}
  maxFiles={3}
/>`}
      >
        <Card className="p-6">
          <FileUploader
            files={emptyFiles}
            onChange={setEmptyFiles}
            label={t('components.calendar.fileUploader.emptyLabel', 'Tệp đính kèm')}
            onUpload={handleUpload}
            maxFiles={3}
          />

          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
            <Typography variant="h6" className="mb-2">
              {t('components.calendar.fileUploader.currentValue', 'Giá trị hiện tại')}:
            </Typography>
            <pre className="text-sm overflow-auto p-2 bg-gray-100 dark:bg-gray-900 rounded">
              {JSON.stringify(emptyFiles, null, 2)}
            </pre>
          </div>
        </Card>
      </ComponentDemo>

      <ComponentDemo
        title={t(
          'components.calendar.fileUploader.restricted.title',
          'Tải lên tệp với giới hạn loại tệp'
        )}
        description={t(
          'components.calendar.fileUploader.restricted.description',
          'Component tải lên tệp với giới hạn loại tệp'
        )}
        code={`import FileUploader from '@/modules/calendar/components/files/FileUploader';
import { AttachmentFile, ACCEPTED_FILE_TYPES } from '@/modules/calendar/components/files/types';

const [imageFiles, setImageFiles] = useState<AttachmentFile[]>([]);

<FileUploader
  files={imageFiles}
  onChange={setImageFiles}
  label="Tải lên hình ảnh"
  onUpload={handleUpload}
  accept={ACCEPTED_FILE_TYPES.IMAGES}
  maxSize={5}
/>`}
      >
        <Card className="p-6">
          <FileUploader
            files={emptyFiles}
            onChange={setEmptyFiles}
            label={t('components.calendar.fileUploader.imageLabel', 'Tải lên hình ảnh')}
            onUpload={handleUpload}
            accept={ACCEPTED_FILE_TYPES.IMAGE}
            maxSize={5}
          />
        </Card>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.calendar.fileUploader.usage.title', 'Cách sử dụng')}
        description={t(
          'components.calendar.fileUploader.usage.description',
          'Hướng dẫn cách sử dụng component FileUploader'
        )}
        code={`// Import component
import FileUploader from '@/modules/calendar/components/files/FileUploader';
import { AttachmentFile, ACCEPTED_FILE_TYPES } from '@/modules/calendar/components/files/types';

// State cho danh sách tệp
const [files, setFiles] = useState<AttachmentFile[]>([]);

// Hàm upload file
const handleUpload = async (file: File): Promise<string> => {
  // Gọi API để lấy URL tải lên
  const response = await fetch('/api/upload-url', {
    method: 'POST',
    body: JSON.stringify({ fileName: file.name, fileType: file.type }),
  });
  const { uploadUrl, fileUrl } = await response.json();

  // Tải lên file
  await fetch(uploadUrl, {
    method: 'PUT',
    body: file,
    headers: { 'Content-Type': file.type },
  });

  // Trả về URL của file
  return fileUrl;
};

// Sử dụng component
<FileUploader
  files={files}                 // Danh sách tệp
  onChange={setFiles}           // Callback khi thay đổi danh sách
  label="Tệp đính kèm"          // Nhãn hiển thị
  onUpload={handleUpload}       // Callback khi tải lên tệp
  accept={ACCEPTED_FILE_TYPES.ALL}  // Loại tệp được chấp nhận
  maxSize={10}                  // Kích thước tối đa (MB)
  maxFiles={5}                  // Số lượng tệp tối đa
  disabled={false}              // Trạng thái disabled
  className="custom-class"      // CSS class bổ sung
  dragDrop={true}               // Cho phép kéo thả
/>`}
      >
        <div className="space-y-4">
          <Typography variant="h6">Props</Typography>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Prop
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Default
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Description
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    files
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    AttachmentFile[]
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    []
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Danh sách tệp
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    onChange
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    Function
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    -
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Callback khi thay đổi danh sách
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    onUpload
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    Function
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    -
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Callback khi tải lên tệp
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    maxFiles
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    number
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    5
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Số lượng tệp tối đa
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default FileUploaderPage;
