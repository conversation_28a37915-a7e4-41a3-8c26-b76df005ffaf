import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, SearchBar, IconCard } from '@/shared/components/common';

import ComponentDemo from '../components/ComponentDemo';

const SearchBarPage: React.FC = () => {
  const { t } = useTranslation();
  const [searchValue1, setSearchValue1] = useState('');
  const [searchValue2, setSearchValue2] = useState('');
  const [searchValue3, setSearchValue3] = useState('');
  const [searchValue4, setSearchValue4] = useState('');
  const [isVisible1, setIsVisible1] = useState(false);
  const [isVisible2, setIsVisible2] = useState(false);

  return (
    <div className="p-4 sm:p-6 space-y-8">
      <div>
        <h1 className="text-2xl font-semibold mb-2 text-foreground">
          {t('components.searchBar.title', 'Search Bar')}
        </h1>
        <p className="text-muted">
          {t(
            'components.searchBar.description',
            'Modern search bar component with animation and various styles.'
          )}
        </p>
      </div>

      <ComponentDemo
        title={t('components.searchBar.basic.title', 'Basic Search Bar')}
        description={t(
          'components.searchBar.basic.description',
          'Basic search bar with toggle functionality.'
        )}
        code={`const [searchValue, setSearchValue] = useState('');
const [isVisible, setIsVisible] = useState(false);

<div className="flex items-center">
  <IconCard
    icon="search"
    variant={isVisible ? "primary" : "default"}
    onClick={() => setIsVisible(!isVisible)}
  />

  <SearchBar
    visible={isVisible}
    value={searchValue}
    onChange={setSearchValue}
    onToggle={() => setIsVisible(!isVisible)}
  />
</div>`}
      >
        <div className="flex items-center">
          <IconCard
            icon="search"
            variant={isVisible1 ? 'primary' : 'default'}
            onClick={() => setIsVisible1(!isVisible1)}
          />

          <SearchBar
            visible={isVisible1}
            value={searchValue1}
            onChange={setSearchValue1}
            onToggle={() => setIsVisible1(!isVisible1)}
          />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.searchBar.variants.title', 'Search Bar Variants')}
        description={t(
          'components.searchBar.variants.description',
          'Search bars with different styles.'
        )}
        code={`<div className="space-y-4">
  <div className="flex items-center">
    <SearchBar
      visible={true}
      value={searchValue1}
      onChange={setSearchValue1}
      variant="default"
      placeholder="Default variant..."
    />
  </div>

  <div className="flex items-center">
    <SearchBar
      visible={true}
      value={searchValue2}
      onChange={setSearchValue2}
      variant="flat"
      placeholder="Flat variant..."
    />
  </div>

  <div className="flex items-center">
    <SearchBar
      visible={true}
      value={searchValue3}
      onChange={setSearchValue3}
      variant="filled"
      placeholder="Filled variant..."
    />
  </div>
</div>`}
      >
        <div className="space-y-4">
          <div className="flex items-center">
            <SearchBar
              visible={true}
              value={searchValue2}
              onChange={setSearchValue2}
              variant="default"
              placeholder="Default variant..."
            />
          </div>

          <div className="flex items-center">
            <SearchBar
              visible={true}
              value={searchValue3}
              onChange={setSearchValue3}
              variant="flat"
              placeholder="Flat variant..."
            />
          </div>

          <div className="flex items-center">
            <SearchBar
              visible={true}
              value={searchValue4}
              onChange={setSearchValue4}
              variant="filled"
              placeholder="Filled variant..."
            />
          </div>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.searchBar.animation.title', 'Search Bar Animation')}
        description={t(
          'components.searchBar.animation.description',
          'Search bar with toggle animation.'
        )}
        code={`const [searchValue, setSearchValue] = useState('');
const [isVisible, setIsVisible] = useState(false);

<div className="flex items-center">
  <Button onClick={() => setIsVisible(!isVisible)}>
    {isVisible ? 'Hide' : 'Show'} Search Bar
  </Button>

  <div className="ml-4">
    <SearchBar
      visible={isVisible}
      value={searchValue}
      onChange={setSearchValue}
      maxWidth="300px"
    />
  </div>
</div>`}
      >
        <div className="flex items-center">
          <Button onClick={() => setIsVisible2(!isVisible2)}>
            {isVisible2
              ? t('components.searchBar.hide', 'Hide')
              : t('components.searchBar.show', 'Show')}{' '}
            {t('components.searchBar.title', 'Search Bar')}
          </Button>

          <div className="ml-4">
            <SearchBar
              visible={isVisible2}
              value={searchValue1}
              onChange={setSearchValue1}
              maxWidth="300px"
            />
          </div>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.searchBar.withoutClear.title', 'Search Bar without Clear Button')}
        description={t(
          'components.searchBar.withoutClear.description',
          'Search bar without the clear button.'
        )}
        code={`<SearchBar
  visible={true}
  value={searchValue}
  onChange={setSearchValue}
  showClearButton={false}
/>`}
      >
        <div className="flex items-center">
          <SearchBar
            visible={true}
            value={searchValue1}
            onChange={setSearchValue1}
            showClearButton={false}
          />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.searchBar.customWidth.title', 'Search Bar with Custom Width')}
        description={t(
          'components.searchBar.customWidth.description',
          'Search bar with custom maximum width.'
        )}
        code={`<SearchBar
  visible={true}
  value={searchValue}
  onChange={setSearchValue}
  maxWidth="500px"
/>`}
      >
        <div className="flex items-center">
          <SearchBar
            visible={true}
            value={searchValue1}
            onChange={setSearchValue1}
            maxWidth="500px"
          />
        </div>
      </ComponentDemo>
    </div>
  );
};

export default SearchBarPage;
