import React, { useEffect, useRef, useState } from 'react';
import { Controller, FieldValues } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Button, Checkbox, Form, FormItem, Input } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { useFormErrors } from '@/shared/hooks';
import { useMutation } from '@tanstack/react-query';

import { useAuth } from '../hooks/useAuth';
import { createLoginSchema, LoginFormValues } from '../schemas/auth.schema';
import { UserAuthService } from '../services';
import {
  areCredentialsValid,
  clearCredentials,
  getCredentials,
  isRememberMeChecked,
  saveCredentials,
} from '../utils/auth-storage.utils';
import RecaptchaModal from './RecaptchaModal';

interface LoginFormProps {
  onSuccess?: () => void;
  onForgotPassword?: () => void;
}

/**
 * Login form component
 */
const LoginForm: React.FC<LoginFormProps> = ({ onSuccess, onForgotPassword }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { setAuth } = useAuth();
  const { formRef, setFormErrors } = useFormErrors<LoginFormValues>();

  // Sử dụng useMutation trực tiếp thay vì thông qua hook useUserAuth
  const loginUser = useMutation({
    mutationFn: (data: { username: string; password: string; recaptchaToken?: string }) => {
      return UserAuthService.login(data);
    },
  });

  // State cho modal reCAPTCHA
  const [showRecaptchaModal, setShowRecaptchaModal] = useState(false);
  const [pendingLoginData, setPendingLoginData] = useState<LoginFormValues | null>(null);

  // Create login schema with translations
  const loginSchema = createLoginSchema(t);

  // Lưu trữ thông tin đăng nhập đã lấy được
  const [savedUsername, setSavedUsername] = useState<string>('');
  const [savedPassword, setSavedPassword] = useState<string>('');
  const [rememberMeChecked, setRememberMeChecked] = useState<boolean>(false);
  const credentialsLoadedRef = useRef<boolean>(false);

  // Check for saved credentials ONLY on component mount and only once
  useEffect(() => {
    // Nếu đã load thông tin đăng nhập rồi thì không load lại nữa
    if (credentialsLoadedRef.current) {
      return;
    }

    // Đánh dấu đã load thông tin đăng nhập
    credentialsLoadedRef.current = true;

    console.log('==========================================');
    console.log('CHECKING FOR SAVED CREDENTIALS (FIRST LOAD ONLY)...');
    console.log('==========================================');

    try {
      // Kiểm tra localStorage trực tiếp
      const rememberMeValue = localStorage.getItem('auth_remember_me');
      const credentialsValue = localStorage.getItem('auth_credentials');

      console.log('Raw localStorage values:', {
        rememberMeValue,
        hasCredentials: !!credentialsValue,
        credentialsLength: credentialsValue ? credentialsValue.length : 0,
      });

      if (credentialsValue) {
        try {
          const parsedCredentials = JSON.parse(credentialsValue);
          console.log('Parsed credentials from localStorage:', {
            username: parsedCredentials.username,
            hasEncryptedPassword: !!parsedCredentials.encryptedPassword,
            timestamp: parsedCredentials.timestamp,
            date: new Date(parsedCredentials.timestamp).toLocaleString(),
          });
        } catch (parseError) {
          console.error('Error parsing credentials from localStorage:', parseError);
        }
      }

      // Kiểm tra xem người dùng đã chọn "Remember me" chưa và thông tin đăng nhập có hợp lệ không
      const isValid = areCredentialsValid();
      console.log('Checking if credentials are valid:', isValid);

      // Lấy trạng thái Remember me
      const isRememberMe = isRememberMeChecked();
      setRememberMeChecked(isRememberMe);
      console.log('Remember me checked:', isRememberMe);

      if (isValid) {
        const savedCredentials = getCredentials();
        console.log('Retrieved credentials:', {
          hasCredentials: !!savedCredentials,
          username: savedCredentials?.username,
          hasPassword: !!savedCredentials?.password,
          passwordLength: savedCredentials?.password ? savedCredentials.password.length : 0,
        });

        if (savedCredentials && savedCredentials.username) {
          console.log('Found valid saved credentials with Remember me checked');

          // Lưu thông tin đăng nhập để sử dụng trong form
          setSavedUsername(savedCredentials.username);
          setSavedPassword(savedCredentials.password);

          console.log('Saved credentials for form:', {
            username: savedCredentials.username,
            passwordLength: savedCredentials.password.length,
          });
        } else {
          console.log('No valid credentials found despite areCredentialsValid returning true');
          clearCredentials();
        }
      } else {
        // Clear expired credentials or if Remember me is not checked
        console.log(
          'No valid credentials found or Remember me not checked, clearing any saved credentials'
        );
        clearCredentials();
      }
    } catch (error) {
      console.error('Error checking saved credentials:', error);
      clearCredentials();
    }

    console.log('==========================================');
  }, []);

  // Handle form submission
  const handleSubmit = (values: unknown) => {
    // Use type assertion with a specific type instead of 'any'
    const loginValues = values as LoginFormValues;

    console.log('==========================================');
    console.log('FORM SUBMITTED');
    console.log('==========================================');
    console.log('Form values:', {
      username: loginValues.username,
      passwordLength: loginValues.password ? loginValues.password.length : 0,
      rememberMe: loginValues.rememberMe,
    });

    // Save credentials if remember me is checked
    if (loginValues.rememberMe) {
      console.log('Remember me is checked, saving credentials for:', loginValues.username);
      if (loginValues.username && loginValues.password) {
        // Lưu thông tin đăng nhập vào localStorage (không cập nhật state)
        saveCredentials(loginValues.username, loginValues.password);

        // Kiểm tra xem đã lưu thành công chưa
        setTimeout(() => {
          const rememberMeValue = localStorage.getItem('auth_remember_me');
          const credentialsValue = localStorage.getItem('auth_credentials');

          console.log('Verification after saving:', {
            rememberMeValue,
            hasCredentials: !!credentialsValue,
          });

          if (credentialsValue) {
            try {
              const parsedCredentials = JSON.parse(credentialsValue);
              console.log('Saved credentials:', {
                username: parsedCredentials.username,
                hasEncryptedPassword: !!parsedCredentials.encryptedPassword,
                timestamp: parsedCredentials.timestamp,
              });
            } catch (parseError) {
              console.error('Error parsing saved credentials:', parseError);
            }
          }
        }, 100);
      } else {
        console.error('Cannot save credentials: username or password is empty');
      }
    } else {
      console.log('Remember me is not checked, clearing credentials');
      clearCredentials();
    }
    console.log('==========================================');

    // Reset form errors
    setFormErrors({});

    // Lưu dữ liệu form và hiển thị modal reCAPTCHA
    setPendingLoginData(loginValues);
    setShowRecaptchaModal(true);
  };

  // Xử lý khi reCAPTCHA thành công
  const handleRecaptchaSuccess = (token: string) => {
    if (!pendingLoginData) return;

    // Call login API với token reCAPTCHA
    loginUser.mutate(
      {
        username: pendingLoginData.username,
        password: pendingLoginData.password,
        recaptchaToken: token,
      },
      {
        onSuccess: (response: ApiResponseDto<unknown>) => {
          // Lưu token vào Redux store thông qua hook useAuth
          if (
            response.result &&
            typeof response.result === 'object' &&
            'accessToken' in response.result &&
            'user' in response.result
          ) {
            const { accessToken, user, permissions } = response.result as {
              accessToken: string;
              user: { id: number; username: string; email: string };
              permissions: string[];
            };

            // Lưu thông tin đăng nhập vào Redux
            setAuth({
              accessToken,
              expiresIn: 3600, // Giả định thời gian hết hạn là 1 giờ
              user: {
                ...user,
                permissions: permissions || [],
              } as any, // Type casting để tránh lỗi TypeScript
            });

            console.log('Token saved to Redux:', accessToken);
            console.log('User saved to Redux:', user);

            // Đăng nhập thành công, không cần xác thực thêm
            // Call success callback if provided
            if (onSuccess) {
              onSuccess();
            }

            // Navigate to home page
            navigate('/');
          } else {
            // Xử lý trường hợp response không đúng định dạng
            console.error('Invalid login response format:', response);
            setFormErrors({
              username: t('auth.loginError', 'Đăng nhập thất bại: Định dạng phản hồi không hợp lệ'),
            });
          }
        },
        onError: (error: unknown) => {
          console.error('Login error:', error);

          // Lấy thông báo lỗi từ response API
          let errorMsg = t('auth.loginError', 'Đăng nhập thất bại');

          // Kiểm tra xem error có phải là AxiosError không
          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as {
              response: { data?: { message?: string; errors?: Record<string, string> } };
            };

            // Nếu có lỗi cụ thể cho từng field
            if (axiosError.response.data?.errors) {
              // Đặt lỗi cho các field tương ứng
              setFormErrors(axiosError.response.data.errors);
            } else if (axiosError.response.data?.message) {
              // Nếu chỉ có thông báo lỗi chung
              errorMsg = axiosError.response.data.message;
              // Đặt lỗi chung cho field username (hoặc field phù hợp)
              setFormErrors({ username: errorMsg });
            }
          } else {
            // Nếu không có lỗi cụ thể, đặt lỗi chung cho field username
            setFormErrors({ username: errorMsg });
          }

          // Reset modal state sau khi đăng nhập thất bại
          setShowRecaptchaModal(false);
          setPendingLoginData(null);
        },
      }
    );
  };

  // Xử lý đóng modal reCAPTCHA
  const handleRecaptchaClose = () => {
    setShowRecaptchaModal(false);
    setPendingLoginData(null);
  };

  return (
    <Form
      ref={formRef as unknown as React.RefObject<FormRef<FieldValues>>}
      schema={loginSchema}
      onSubmit={handleSubmit}
      className="space-y-4"
      autoComplete="off"
      // Chỉ sử dụng thông tin đăng nhập đã lưu khi form được tạo lần đầu
      // Không tự động cập nhật khi bấm đăng nhập hoặc thực hiện các hành động khác
      defaultValues={{
        username: savedUsername,
        password: savedPassword,
        rememberMe: rememberMeChecked, // Sử dụng trạng thái Remember me đã lưu
      }}
    >
      <FormItem name="username" label={t('auth.email')} required>
        <Input type="email" fullWidth autoComplete="off" />
      </FormItem>

      <FormItem name="password" label={t('auth.password')} required>
        <Input
          type="password"
          fullWidth
          autoComplete="new-password" // Trick để ngăn trình duyệt tự động điền
        />
      </FormItem>

      <div className="flex justify-between items-start mb-4">
        <Controller
          name="rememberMe"
          render={({ field }) => (
            <div className="flex items-center">
              <Checkbox
                checked={field.value}
                onChange={field.onChange}
                label={t('auth.rememberMe')}
                variant="filled"
                color="primary"
                size="md"
              />
            </div>
          )}
        />

        <a
          href="#"
          onClick={e => {
            e.preventDefault();
            onForgotPassword?.();
          }}
          className="text-primary hover:text-primary/80 text-sm font-medium"
        >
          {t('auth.forgotPassword')}
        </a>
      </div>

      <Button type="submit" variant="primary" fullWidth isLoading={loginUser.isPending}>
        {t('auth.signIn')}
      </Button>

      {/* Modal reCAPTCHA */}
      <RecaptchaModal
        isOpen={showRecaptchaModal}
        onClose={handleRecaptchaClose}
        onSuccess={handleRecaptchaSuccess}
      />
    </Form>
  );
};

export default LoginForm;
