import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  RecurrenceOptions,
  RecurrenceType,
  RecurrenceEndType,
  Weekday,
} from '@/modules/calendar/components/recurrence/recurrence.types';
import RecurrenceSelector from '@/modules/calendar/components/recurrence/RecurrenceSelector';
import { Card, Typography } from '@/shared/components/common';

import { ComponentDemo } from '../../components';

/**
 * Trang demo cho RecurrenceSelector
 */
const RecurrencePage: React.FC = () => {
  const { t } = useTranslation();
  const [recurrence, setRecurrence] = useState<RecurrenceOptions | null>(null);
  const [recurrenceDaily, setRecurrenceDaily] = useState<RecurrenceOptions>({
    config: {
      type: RecurrenceType.DAILY,
      interval: 1,
    },
    end: {
      type: RecurrenceEndType.NEVER,
    },
    startDate: new Date(),
  });
  const [recurrenceWeekly, setRecurrenceWeekly] = useState<RecurrenceOptions>({
    config: {
      type: RecurrenceType.WEEKLY,
      interval: 1,
      byDay: [Weekday.MONDAY, Weekday.WEDNESDAY, Weekday.FRIDAY],
    },
    end: {
      type: RecurrenceEndType.AFTER,
      count: 10,
    },
    startDate: new Date(),
  });

  return (
    <div className="p-4 sm:p-6 space-y-8">
      <div>
        <h1 className="text-2xl font-semibold mb-2 text-foreground">
          {t('components.calendar.recurrence.title', 'Recurrence Selector')}
        </h1>
        <p className="text-muted">
          {t(
            'components.calendar.recurrence.description',
            'Component cấu hình lặp lại sự kiện với nhiều tùy chọn'
          )}
        </p>
      </div>

      <ComponentDemo
        title={t('components.calendar.recurrence.basic.title', 'Cấu hình lặp lại cơ bản')}
        description={t(
          'components.calendar.recurrence.basic.description',
          'Component cấu hình lặp lại với giá trị ban đầu là null'
        )}
        code={`import RecurrenceSelector from '@/modules/calendar/components/recurrence/RecurrenceSelector';
import { RecurrenceOptions } from '@/modules/calendar/components/recurrence/recurrence.types';

const [recurrence, setRecurrence] = useState<RecurrenceOptions | null>(null);

<RecurrenceSelector
  value={recurrence}
  onChange={setRecurrence}
  label="Lặp lại"
/>`}
      >
        <Card className="p-6">
          <RecurrenceSelector
            value={recurrence}
            onChange={value => setRecurrence(value)}
            label={t('components.calendar.recurrence.label', 'Lặp lại')}
            eventStartDate={new Date()}
          />

          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
            <Typography variant="h6" className="mb-2">
              {t('components.calendar.recurrence.currentValue', 'Giá trị hiện tại')}:
            </Typography>
            <pre className="text-sm overflow-auto p-2 bg-gray-100 dark:bg-gray-900 rounded">
              {JSON.stringify(recurrence, null, 2)}
            </pre>
          </div>
        </Card>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.calendar.recurrence.daily.title', 'Lặp lại hàng ngày')}
        description={t(
          'components.calendar.recurrence.daily.description',
          'Component cấu hình lặp lại hàng ngày'
        )}
        code={`import RecurrenceSelector from '@/modules/calendar/components/recurrence/RecurrenceSelector';
import { RecurrenceOptions, RecurrenceType } from '@/modules/calendar/components/recurrence/recurrence.types';

const [recurrenceDaily, setRecurrenceDaily] = useState<RecurrenceOptions>({
  type: RecurrenceType.DAILY,
  interval: 1,
  end: {
    type: 'never'
  }
});

<RecurrenceSelector
  value={recurrenceDaily}
  onChange={setRecurrenceDaily}
  label="Lặp lại hàng ngày"
/>`}
      >
        <Card className="p-6">
          <RecurrenceSelector
            value={recurrenceDaily}
            onChange={value => value && setRecurrenceDaily(value)}
            label={t('components.calendar.recurrence.dailyLabel', 'Lặp lại hàng ngày')}
            eventStartDate={new Date()}
          />

          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
            <Typography variant="h6" className="mb-2">
              {t('components.calendar.recurrence.currentValue', 'Giá trị hiện tại')}:
            </Typography>
            <pre className="text-sm overflow-auto p-2 bg-gray-100 dark:bg-gray-900 rounded">
              {JSON.stringify(recurrenceDaily, null, 2)}
            </pre>
          </div>
        </Card>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.calendar.recurrence.weekly.title', 'Lặp lại hàng tuần')}
        description={t(
          'components.calendar.recurrence.weekly.description',
          'Component cấu hình lặp lại hàng tuần với các ngày trong tuần'
        )}
        code={`import RecurrenceSelector from '@/modules/calendar/components/recurrence/RecurrenceSelector';
import { RecurrenceOptions, RecurrenceType } from '@/modules/calendar/components/recurrence/recurrence.types';

const [recurrenceWeekly, setRecurrenceWeekly] = useState<RecurrenceOptions>({
  type: RecurrenceType.WEEKLY,
  interval: 1,
  weekdays: ['monday', 'wednesday', 'friday'],
  end: {
    type: 'after',
    occurrences: 10
  }
});

<RecurrenceSelector
  value={recurrenceWeekly}
  onChange={setRecurrenceWeekly}
  label="Lặp lại hàng tuần"
/>`}
      >
        <Card className="p-6">
          <RecurrenceSelector
            value={recurrenceWeekly}
            onChange={value => value && setRecurrenceWeekly(value)}
            label={t('components.calendar.recurrence.weeklyLabel', 'Lặp lại hàng tuần')}
            eventStartDate={new Date()}
          />

          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
            <Typography variant="h6" className="mb-2">
              {t('components.calendar.recurrence.currentValue', 'Giá trị hiện tại')}:
            </Typography>
            <pre className="text-sm overflow-auto p-2 bg-gray-100 dark:bg-gray-900 rounded">
              {JSON.stringify(recurrenceWeekly, null, 2)}
            </pre>
          </div>
        </Card>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.calendar.recurrence.usage.title', 'Cách sử dụng')}
        description={t(
          'components.calendar.recurrence.usage.description',
          'Hướng dẫn cách sử dụng component RecurrenceSelector'
        )}
        code={`// Import component
import RecurrenceSelector from '@/modules/calendar/components/recurrence/RecurrenceSelector';
import { RecurrenceOptions } from '@/modules/calendar/components/recurrence/recurrence.types';

// State cho cấu hình lặp lại
const [recurrence, setRecurrence] = useState<RecurrenceOptions | null>(null);

// Sử dụng component
<RecurrenceSelector
  value={recurrence}           // Giá trị cấu hình lặp lại
  onChange={setRecurrence}     // Callback khi thay đổi cấu hình
  label="Lặp lại"              // Nhãn hiển thị
  disabled={false}             // Trạng thái disabled
  className="custom-class"     // CSS class bổ sung
/>`}
      >
        <div className="space-y-4">
          <Typography variant="h6">Props</Typography>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Prop
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Default
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Description
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    value
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    RecurrenceOptions | null
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    null
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Giá trị cấu hình lặp lại
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    onChange
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    Function
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    -
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Callback khi thay đổi cấu hình
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    label
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    string
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    ''
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Nhãn hiển thị
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    disabled
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    boolean
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    false
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Trạng thái disabled
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default RecurrencePage;
