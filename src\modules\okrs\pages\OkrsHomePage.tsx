import React from 'react';
import { useTranslation } from 'react-i18next';

import ModuleCard from '@/modules/components/card/ModuleCard';
import { ResponsiveGrid } from '@/shared/components/common';

/**
 * Trang chủ module OKRs
 */
const OkrsHomePage: React.FC = () => {
  const { t } = useTranslation(['common', 'okrs']);

  // <PERSON>h sách các module con của OKRs
  const subModules = [
    {
      id: 'cycles',
      title: t('okrs:modules.cycles.title', '<PERSON> kỳ OKR'),
      description: t('okrs:modules.cycles.description', 'Quản lý các chu kỳ OKR (quý, năm)'),
      icon: 'calendar',
      count: 4,
      countLabel: t('okrs:modules.cycles.countLabel', 'Chu kỳ'),
      linkTo: '/okrs/cycles',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'objectives',
      title: t('okrs:modules.objectives.title', '<PERSON><PERSON><PERSON> tiêu'),
      description: t(
        'okrs:modules.objectives.description',
        'Quản lý mục tiêu của tổ chức và cá nhân'
      ),
      icon: 'award',
      count: 8,
      countLabel: t('okrs:modules.objectives.countLabel', 'Mục tiêu'),
      linkTo: '/okrs/objectives',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'keyResults',
      title: t('okrs:modules.keyResults.title', 'Kết quả then chốt'),
      description: t('okrs:modules.keyResults.description', 'Quản lý các kết quả then chốt'),
      icon: 'check',
      count: 24,
      countLabel: t('okrs:modules.keyResults.countLabel', 'Kết quả'),
      linkTo: '/okrs/key-results',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'alignment',
      title: t('okrs:modules.alignment.title', 'Sự liên kết'),
      description: t('okrs:modules.alignment.description', 'Xem sự liên kết giữa các mục tiêu'),
      icon: 'link',
      count: 5,
      countLabel: t('okrs:modules.alignment.countLabel', 'Liên kết'),
      linkTo: '/okrs/alignment',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'progress',
      title: t('okrs:modules.progress.title', 'Tiến độ'),
      description: t('okrs:modules.progress.description', 'Theo dõi tiến độ thực hiện OKRs'),
      icon: 'trending-up',
      count: 75,
      countLabel: t('okrs:modules.progress.countLabel', '% hoàn thành'),
      linkTo: '/okrs/progress',
      linkText: t('common:view', 'Xem'),
    },
  ];

  return (
    <div>
      <ResponsiveGrid gap={4} maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 4 }}>
        {subModules.map(module => (
          <ModuleCard
            key={module.id}
            title={module.title}
            description={module.description}
            icon={module.icon}
            count={module.count}
            countLabel={module.countLabel}
            linkTo={module.linkTo}
            linkText={module.linkText}
            className="h-full"
          />
        ))}
      </ResponsiveGrid>
    </div>
  );
};

export default OkrsHomePage;
