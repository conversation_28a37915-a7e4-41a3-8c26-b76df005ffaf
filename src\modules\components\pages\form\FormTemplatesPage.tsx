import React from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import {
  Form,
  FormItem,
  Input,
  Button,
  FormGrid,
  FormSection,
  Toggle,
  Icon,
} from '@/shared/components/common';
import { IconName } from '@/shared/components/common/Icon/Icon';

import { ComponentDemo } from '../../components';

/**
 * Trang hiển thị các mẫu form phổ biến
 */
const FormTemplatesPage: React.FC = () => {
  const { t } = useTranslation();

  // Login form schema
  const loginFormSchema = z.object({
    email: z.string().min(1, 'Email is required').email('Invalid email format'),
    password: z
      .string()
      .min(1, 'Password is required')
      .min(8, 'Password must be at least 8 characters'),
    rememberMe: z.boolean().optional(),
  });

  // Registration form schema
  const registrationFormSchema = z
    .object({
      firstName: z.string().min(1, 'First name is required'),
      lastName: z.string().min(1, 'Last name is required'),
      email: z.string().min(1, '<PERSON><PERSON> is required').email('Invalid email format'),
      password: z
        .string()
        .min(8, 'Password must be at least 8 characters')
        .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
        .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
        .regex(/\d/, 'Password must contain at least one number'),
      confirmPassword: z.string().min(1, 'Please confirm your password'),
      agreeTerms: z
        .boolean()
        .refine(val => val === true, 'You must agree to the terms and conditions'),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: 'Passwords do not match',
      path: ['confirmPassword'],
    });

  // Contact form schema
  const contactFormSchema = z.object({
    name: z.string().min(1, 'Name is required'),
    email: z.string().min(1, 'Email is required').email('Invalid email format'),
    subject: z.string().min(1, 'Subject is required'),
    message: z.string().min(10, 'Message must be at least 10 characters'),
  });

  // Profile form schema
  const profileFormSchema = z.object({
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    email: z.string().min(1, 'Email is required').email('Invalid email format'),
    phone: z.string().optional(),
    bio: z.string().optional(),
    address: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zipCode: z.string().optional(),
    country: z.string().optional(),
  });

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.form.templates.title', 'Form Templates')}
        </h1>
        <p className="text-muted">
          {t(
            'components.form.templates.description',
            'Ready-to-use form templates for common use cases.'
          )}
        </p>
      </div>

      {/* Login Form Template */}
      <ComponentDemo
        title={t('components.form.templates.login.title', 'Login Form')}
        description={t(
          'components.form.templates.login.description',
          'A standard login form with email, password, and remember me option.'
        )}
        code={`import { Form, FormItem, Input, Button, Toggle, Icon } from '@/shared/components/common';
import { z } from 'zod';

// Define schema
const loginSchema = z.object({
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
  password: z.string().min(1, 'Password is required').min(8, 'Password must be at least 8 characters'),
  rememberMe: z.boolean().optional(),
});

// Handle form submission
const handleSubmit = (values) => {
  console.log('Form submitted:', values);
  // In a real app, you would call your API here
};

// Render form
<Form
  schema={loginSchema}
  onSubmit={handleSubmit}
  className="space-y-4"
>
  <FormItem name="email" label="Email" required>
    <Input
      type="email"
      placeholder="Enter your email"
      leftIcon={<Icon name="mail" size="sm" />}
      fullWidth
    />
  </FormItem>

  <FormItem name="password" label="Password" required>
    <Input
      type="password"
      placeholder="Enter your password"
      leftIcon={<Icon name="lock" size="sm" />}
      fullWidth
    />
  </FormItem>

  <FormItem name="rememberMe" inline>
    <Toggle />
    <span className="ml-2">Remember me</span>
  </FormItem>

  <Button type="submit" fullWidth>
    Log In
  </Button>
</Form>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Form
            schema={loginFormSchema}
            onSubmit={values => console.log(values)}
            className="space-y-4"
          >
            <FormItem name="email" label="Email" required>
              <Input
                type="email"
                placeholder="Enter your email"
                leftIcon={<Icon name="mail" size="sm" />}
                fullWidth
              />
            </FormItem>

            <FormItem name="password" label="Password" required>
              <Input
                type="password"
                placeholder="Enter your password"
                leftIcon={<Icon name={'lock' as IconName} size="sm" />}
                fullWidth
              />
            </FormItem>

            <div className="flex items-center">
              <FormItem name="rememberMe" inline>
                <Toggle />
              </FormItem>
              <span className="ml-2">Remember me</span>
            </div>

            <Button type="submit" fullWidth>
              Log In
            </Button>
          </Form>
        </div>
      </ComponentDemo>

      {/* Registration Form Template */}
      <ComponentDemo
        title={t('components.form.templates.registration.title', 'Registration Form')}
        description={t(
          'components.form.templates.registration.description',
          'A complete registration form with validation.'
        )}
        code={`import { Form, FormItem, Input, Button, Toggle, FormGrid } from '@/shared/components/common';
import { z } from 'zod';

// Define schema
const registrationSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  confirmPassword: z.string().min(1, 'Please confirm your password'),
  agreeTerms: z.boolean().refine(val => val === true, 'You must agree to the terms and conditions'),
}).refine(data => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
});

// Render form
<Form
  schema={registrationSchema}
  onSubmit={handleSubmit}
  className="space-y-4"
>
  <FormGrid columns={2} gap="md">
    <FormItem name="firstName" label="First Name" required>
      <Input placeholder="Enter first name" fullWidth />
    </FormItem>

    <FormItem name="lastName" label="Last Name" required>
      <Input placeholder="Enter last name" fullWidth />
    </FormItem>
  </FormGrid>

  <FormItem name="email" label="Email" required>
    <Input type="email" placeholder="Enter email" fullWidth />
  </FormItem>

  <FormItem name="password" label="Password" required>
    <Input type="password" placeholder="Enter password" fullWidth />
  </FormItem>

  <FormItem name="confirmPassword" label="Confirm Password" required>
    <Input type="password" placeholder="Confirm password" fullWidth />
  </FormItem>

  <FormItem name="agreeTerms" inline>
    <Toggle />
    <span className="ml-2">I agree to the terms and conditions</span>
  </FormItem>

  <Button type="submit" fullWidth>
    Register
  </Button>
</Form>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Form
            schema={registrationFormSchema}
            onSubmit={values => console.log(values)}
            className="space-y-4"
          >
            <FormGrid columns={2} gap="md">
              <FormItem name="firstName" label="First Name" required>
                <Input placeholder="Enter first name" fullWidth />
              </FormItem>

              <FormItem name="lastName" label="Last Name" required>
                <Input placeholder="Enter last name" fullWidth />
              </FormItem>
            </FormGrid>

            <FormItem name="email" label="Email" required>
              <Input type="email" placeholder="Enter email" fullWidth />
            </FormItem>

            <FormItem name="password" label="Password" required>
              <Input type="password" placeholder="Enter password" fullWidth />
            </FormItem>

            <FormItem name="confirmPassword" label="Confirm Password" required>
              <Input type="password" placeholder="Confirm password" fullWidth />
            </FormItem>

            <div className="flex items-center">
              <FormItem name="agreeTerms" inline>
                <Toggle />
              </FormItem>
              <span className="ml-2">I agree to the terms and conditions</span>
            </div>

            <Button type="submit" fullWidth>
              Register
            </Button>
          </Form>
        </div>
      </ComponentDemo>

      {/* Contact Form Template */}
      <ComponentDemo
        title={t('components.form.templates.contact.title', 'Contact Form')}
        description={t(
          'components.form.templates.contact.description',
          'A contact form with name, email, subject, and message fields.'
        )}
        code={`import { Form, FormItem, Input, Button } from '@/shared/components/common';
import { z } from 'zod';

// Define schema
const contactSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
  subject: z.string().min(1, 'Subject is required'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
});

// Render form
<Form
  schema={contactSchema}
  onSubmit={handleSubmit}
  className="space-y-4"
>
  <FormItem name="name" label="Name" required>
    <Input placeholder="Enter your name" fullWidth />
  </FormItem>

  <FormItem name="email" label="Email" required>
    <Input type="email" placeholder="Enter your email" fullWidth />
  </FormItem>

  <FormItem name="subject" label="Subject" required>
    <Input placeholder="Enter subject" fullWidth />
  </FormItem>

  <FormItem name="message" label="Message" required>
    <Input
      as="textarea"
      placeholder="Enter your message"
      rows={5}
      fullWidth
    />
  </FormItem>

  <Button type="submit">
    Send Message
  </Button>
</Form>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Form
            schema={contactFormSchema}
            onSubmit={values => console.log(values)}
            className="space-y-4"
          >
            <FormItem name="name" label="Name" required>
              <Input placeholder="Enter your name" fullWidth />
            </FormItem>

            <FormItem name="email" label="Email" required>
              <Input type="email" placeholder="Enter your email" fullWidth />
            </FormItem>

            <FormItem name="subject" label="Subject" required>
              <Input placeholder="Enter subject" fullWidth />
            </FormItem>

            <FormItem name="message" label="Message" required>
              <textarea
                className="w-full p-2 border rounded"
                placeholder="Enter your message"
                rows={5}
              />
            </FormItem>

            <Button type="submit">Send Message</Button>
          </Form>
        </div>
      </ComponentDemo>

      {/* Profile Form Template */}
      <ComponentDemo
        title={t('components.form.templates.profile.title', 'Profile Form')}
        description={t(
          'components.form.templates.profile.description',
          'A user profile form with personal and contact information.'
        )}
        code={`import { Form, FormItem, Input, Button, FormSection, FormGrid } from '@/shared/components/common';
import { z } from 'zod';

// Define schema
const profileSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
  phone: z.string().optional(),
  bio: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),
  country: z.string().optional(),
});

// Render form
<Form
  schema={profileSchema}
  onSubmit={handleSubmit}
  className="space-y-6"
>
  <FormSection
    title="Personal Information"
    description="Your personal details"
    collapsible
    defaultExpanded={true}
  >
    <FormGrid columns={2} gap="md">
      <FormItem name="firstName" label="First Name" required>
        <Input placeholder="Enter first name" fullWidth />
      </FormItem>

      <FormItem name="lastName" label="Last Name" required>
        <Input placeholder="Enter last name" fullWidth />
      </FormItem>
    </FormGrid>

    <FormItem name="email" label="Email" required>
      <Input type="email" placeholder="Enter email" fullWidth />
    </FormItem>

    <FormItem name="phone" label="Phone">
      <Input placeholder="Enter phone number" fullWidth />
    </FormItem>

    <FormItem name="bio" label="Bio">
      <Input
        as="textarea"
        placeholder="Tell us about yourself"
        rows={3}
        fullWidth
      />
    </FormItem>
  </FormSection>

  <FormSection
    title="Address Information"
    description="Your address details"
    collapsible
    defaultExpanded={true}
  >
    <FormItem name="address" label="Address">
      <Input placeholder="Enter address" fullWidth />
    </FormItem>

    <FormGrid columns={2} gap="md">
      <FormItem name="city" label="City">
        <Input placeholder="Enter city" fullWidth />
      </FormItem>

      <FormItem name="state" label="State/Province">
        <Input placeholder="Enter state" fullWidth />
      </FormItem>

      <FormItem name="zipCode" label="Zip/Postal Code">
        <Input placeholder="Enter zip code" fullWidth />
      </FormItem>

      <FormItem name="country" label="Country">
        <Input placeholder="Enter country" fullWidth />
      </FormItem>
    </FormGrid>
  </FormSection>

  <Button type="submit">
    Save Profile
  </Button>
</Form>`}
      >
        <div className="w-full max-w-2xl mx-auto">
          <Form
            schema={profileFormSchema}
            onSubmit={values => console.log(values)}
            className="space-y-6"
          >
            <FormSection
              title="Personal Information"
              description="Your personal details"
              collapsible
              defaultExpanded={true}
            >
              <FormGrid columns={2} gap="md">
                <FormItem name="firstName" label="First Name" required>
                  <Input placeholder="Enter first name" fullWidth />
                </FormItem>

                <FormItem name="lastName" label="Last Name" required>
                  <Input placeholder="Enter last name" fullWidth />
                </FormItem>
              </FormGrid>

              <FormItem name="email" label="Email" required>
                <Input type="email" placeholder="Enter email" fullWidth />
              </FormItem>

              <FormItem name="phone" label="Phone">
                <Input placeholder="Enter phone number" fullWidth />
              </FormItem>

              <FormItem name="bio" label="Bio">
                <textarea
                  className="w-full p-2 border rounded"
                  placeholder="Tell us about yourself"
                  rows={3}
                />
              </FormItem>
            </FormSection>

            <FormSection
              title="Address Information"
              description="Your address details"
              collapsible
              defaultExpanded={true}
            >
              <FormItem name="address" label="Address">
                <Input placeholder="Enter address" fullWidth />
              </FormItem>

              <FormGrid columns={2} gap="md">
                <FormItem name="city" label="City">
                  <Input placeholder="Enter city" fullWidth />
                </FormItem>

                <FormItem name="state" label="State/Province">
                  <Input placeholder="Enter state" fullWidth />
                </FormItem>

                <FormItem name="zipCode" label="Zip/Postal Code">
                  <Input placeholder="Enter zip code" fullWidth />
                </FormItem>

                <FormItem name="country" label="Country">
                  <Input placeholder="Enter country" fullWidth />
                </FormItem>
              </FormGrid>
            </FormSection>

            <Button type="submit">Save Profile</Button>
          </Form>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default FormTemplatesPage;
