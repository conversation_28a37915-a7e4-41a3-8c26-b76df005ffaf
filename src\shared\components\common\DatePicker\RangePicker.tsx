import { Icon } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';
import {
  autoUpdate,
  flip,
  FloatingFocusManager,
  FloatingPortal,
  offset,
  Placement,
  shift,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
  useRole,
} from '@floating-ui/react';
import React, { forwardRef, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import Calendar from './Calendar';
import { RangePickerProps } from './types';
import { formatDate, getDaysBetween, parseDate, validateDate } from './utils';

/**
 * RangePicker component cho phép người dùng chọn khoảng thời gian (từ ngày đến ngày).
 *
 * Component này mở rộng từ DatePicker và cung cấp giao diện để chọn hai ngày (bắt đầu và kết thúc),
 * với nhiều tùy chọn như hiển thị hai tháng cạnh nhau, hiển thị số ngày đã chọn, và nhiều tính năng khác.
 *
 * @example
 * ```tsx
 * import { RangePicker } from '@/shared/components/common';
 * import { useState } from 'react';
 *
 * const MyComponent = () => {
 *   const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
 *
 *   return (
 *     <RangePicker
 *       label="Chọn khoảng thời gian"
 *       value={dateRange}
 *       onChange={setDateRange}
 *       placeholder={['Ngày bắt đầu', 'Ngày kết thúc']}
 *       showDaysCount
 *     />
 *   );
 * };
 * ```
 *
 * @see DatePicker Nếu bạn chỉ cần chọn một ngày
 */
const RangePicker = forwardRef<HTMLInputElement, RangePickerProps>(
  (
    {
      value = [null, null],
      onChange,
      format = 'dd/MM/yyyy',
      placeholder = ['Start date', 'End date'],
      label,
      disabled = false,
      disabledDates,
      minDate,
      maxDate,
      clearable = true,
      placement = 'bottom',
      size = 'md',
      fullWidth = false,
      error,
      helperText,
      className = '',
      showCalendarIcon = true,
      autoClose = true,
      showToday = true,
      showWeekNumbers = false,
      firstDayOfWeek = 1,
      weekDayNames,
      monthNames,
      showTodayButton = false,
      todayButtonText,
      clearButtonText,
      onOpen,
      onClose,
      // inputRef não é usado diretamente, mas mantido para compatibilidade
      inputProps,
      calendarIcon,
      // maxRange não é usado atualmente
      showDaysCount = false,
      separatorText = 'đến',
      showTwoMonths = false,
      autoSelectEnd = false,
    },
    // Bỏ qua ref từ forwardRef vì chúng ta sử dụng inputRef
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _ref
  ) => {
    // Không sử dụng ref từ forwardRef
    const { t, i18n } = useTranslation();
    useTheme(); // Sử dụng hook theme mới

    // Refs
    const containerRef = useRef<HTMLDivElement>(null);
    const startInputRef = useRef<HTMLInputElement>(null);
    const endInputRef = useRef<HTMLInputElement>(null);

    // States
    const [isOpen, setIsOpen] = useState(false);
    const [startInputValue, setStartInputValue] = useState(() =>
      formatDate(value[0], format, i18n.language)
    );
    const [endInputValue, setEndInputValue] = useState(() =>
      formatDate(value[1], format, i18n.language)
    );
    const [calendarMonth, setCalendarMonth] = useState(() => validateDate(value[0]) || new Date());
    // activeInput được sử dụng để theo dõi input nào đang active
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [_activeInput, setActiveInput] = useState<'start' | 'end'>('start');

    // Floating UI setup
    const floatingPlacement = useMemo<Placement>(
      () =>
        placement === 'left'
          ? 'left-start'
          : placement === 'right'
            ? 'right-start'
            : placement === 'top'
              ? 'top-start'
              : 'bottom-start',
      [placement]
    );

    const { refs, floatingStyles, context } = useFloating({
      open: isOpen,
      onOpenChange: setIsOpen,
      placement: floatingPlacement,
      middleware: [
        offset(5),
        flip({
          fallbackAxisSideDirection: 'end',
          crossAxis: false,
        }),
        shift(),
      ],
      whileElementsMounted: autoUpdate,
    });

    const click = useClick(context);
    const dismiss = useDismiss(context, {
      outsidePress: true,
      outsidePressEvent: 'mousedown',
      escapeKey: true,
      referencePress: false,
      referencePressEvent: 'mousedown',
      ancestorScroll: true,
    });
    const role = useRole(context);

    const { getReferenceProps, getFloatingProps } = useInteractions([click, dismiss, role]);

    // Cập nhật giá trị input khi value thay đổi
    useEffect(() => {
      setStartInputValue(formatDate(value[0], format, i18n.language));
      setEndInputValue(formatDate(value[1], format, i18n.language));
    }, [value, format, i18n.language]);

    // Xử lý khi calendar mở/đóng
    useEffect(() => {
      if (isOpen) {
        onOpen?.();
      } else {
        onClose?.();
      }
    }, [isOpen, onOpen, onClose]);

    // Xử lý khi input start thay đổi
    const handleStartInputChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value;
        setStartInputValue(newValue);

        // Parse giá trị input thành Date
        const parsedDate = parseDate(newValue, format, i18n.language);
        if (parsedDate) {
          onChange?.([parsedDate, value[1]]);
          setCalendarMonth(parsedDate);
        }
      },
      [format, i18n.language, onChange, value]
    );

    // Xử lý khi input end thay đổi
    const handleEndInputChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value;
        setEndInputValue(newValue);

        // Parse giá trị input thành Date
        const parsedDate = parseDate(newValue, format, i18n.language);
        if (parsedDate) {
          onChange?.([value[0], parsedDate]);
        }
      },
      [format, i18n.language, onChange, value]
    );

    // Xử lý khi focus vào input start
    const handleStartInputFocus = useCallback(() => {
      if (!disabled) {
        setIsOpen(true);
        setActiveInput('start');
      }
    }, [disabled]);

    // Xử lý khi focus vào input end
    const handleEndInputFocus = useCallback(() => {
      if (!disabled) {
        setIsOpen(true);
        setActiveInput('end');
      }
    }, [disabled]);

    // Xử lý khi nhấn phím Escape
    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent) => {
        if (e.key === 'Escape' && isOpen) {
          setIsOpen(false);
        }
      },
      [isOpen]
    );

    // Xử lý khi chọn range từ calendar
    const handleRangeSelect = useCallback(
      (start: Date | null, end: Date | null) => {
        onChange?.([start, end]);

        if (start) {
          setStartInputValue(formatDate(start, format, i18n.language));
        }

        if (end) {
          setEndInputValue(formatDate(end, format, i18n.language));
        }

        if (autoClose && start && end) {
          setIsOpen(false);
        }

        // Auto focus end input sau khi chọn start date
        if (autoSelectEnd && start && !end) {
          endInputRef.current?.focus();
        }
      },
      [onChange, format, i18n.language, autoClose, autoSelectEnd, endInputRef]
    );

    // Xử lý khi click vào icon calendar
    const handleCalendarIconClick = useCallback(
      (inputType: 'start' | 'end') => {
        if (!disabled) {
          setIsOpen(!isOpen);
          setActiveInput(inputType);
          if (!isOpen) {
            if (inputType === 'start') {
              startInputRef.current?.focus();
            } else {
              endInputRef.current?.focus();
            }
          }
        }
      },
      [disabled, isOpen, startInputRef, endInputRef]
    );

    // Xử lý khi click vào icon clear
    const handleClearClick = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        onChange?.([null, null]);
        setStartInputValue('');
        setEndInputValue('');
      },
      [onChange]
    );

    // Xử lý khi tháng thay đổi trong calendar
    const handleMonthChange = useCallback((newMonth: Date) => {
      setCalendarMonth(newMonth);
    }, []);

    // Size classes
    const sizeClasses = {
      sm: 'h-8 text-sm',
      md: 'h-10',
      lg: 'h-12 text-lg',
    }[size];

    // Width class
    const widthClass = fullWidth ? 'w-full' : '';

    // Base classes
    const baseClasses = 'relative';

    // Combine all classes
    const containerClasses = [baseClasses, widthClass, className].join(' ');

    // Placement classes não são mais necessários com Floating UI
    // const placementClasses = {
    //   top: 'bottom-full mb-1',
    //   bottom: 'top-full mt-1',
    //   left: 'right-full mr-1',
    //   right: 'left-full ml-1',
    // }[placement];

    // Tính số ngày trong range
    const daysCount = value[0] && value[1] ? getDaysBetween(value[0], value[1]) : 0;

    return (
      <div className={containerClasses} ref={containerRef}>
        {/* Label */}
        {label && (
          <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
            {label}
          </label>
        )}

        {/* Inputs container */}
        <div
          className="flex items-center space-x-2"
          ref={refs.setReference}
          {...getReferenceProps()}
        >
          {/* Start date input */}
          <div className="relative flex-1">
            <input
              ref={startInputRef}
              type="text"
              value={startInputValue}
              onChange={handleStartInputChange}
              onFocus={handleStartInputFocus}
              onKeyDown={handleKeyDown}
              placeholder={Array.isArray(placeholder) ? placeholder[0] : 'Start date'}
              disabled={disabled}
              className={`
              w-full px-3 py-2 rounded-md border
              ${error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
              ${disabled ? 'bg-gray-100 dark:bg-gray-800 cursor-not-allowed' : 'bg-white dark:bg-dark-light'}
              ${sizeClasses}
              focus:outline-none focus:ring-2 focus:ring-primary/30 dark:focus:ring-primary-light/30
            `}
              {...inputProps}
            />

            {/* Calendar icon */}
            {showCalendarIcon && (
              <div
                className={`absolute right-3 top-1/2 transform -translate-y-1/2 ${disabled ? 'opacity-50' : 'cursor-pointer'}`}
                onClick={() => handleCalendarIconClick('start')}
              >
                {calendarIcon || <Icon name="calendar" size={size === 'lg' ? 'md' : 'sm'} />}
              </div>
            )}
          </div>

          {/* Separator */}
          <div className="text-gray-500 dark:text-gray-400 flex-shrink-0">{separatorText}</div>

          {/* End date input */}
          <div className="relative flex-1">
            <input
              ref={endInputRef}
              type="text"
              value={endInputValue}
              onChange={handleEndInputChange}
              onFocus={handleEndInputFocus}
              onKeyDown={handleKeyDown}
              placeholder={Array.isArray(placeholder) ? placeholder[1] : 'End date'}
              disabled={disabled}
              className={`
              w-full px-3 py-2 rounded-md border
              ${error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
              ${disabled ? 'bg-gray-100 dark:bg-gray-800 cursor-not-allowed' : 'bg-white dark:bg-dark-light'}
              ${sizeClasses}
              focus:outline-none focus:ring-2 focus:ring-primary/30 dark:focus:ring-primary-light/30
            `}
              {...inputProps}
            />

            {/* Calendar icon */}
            {showCalendarIcon && (
              <div
                className={`absolute right-3 top-1/2 transform -translate-y-1/2 ${disabled ? 'opacity-50' : 'cursor-pointer'}`}
                onClick={() => handleCalendarIconClick('end')}
              >
                {calendarIcon || <Icon name="calendar" size={size === 'lg' ? 'md' : 'sm'} />}
              </div>
            )}
          </div>

          {/* Clear button */}
          {clearable && (startInputValue || endInputValue) && !disabled && (
            <div
              className="cursor-pointer text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 flex-shrink-0"
              onClick={handleClearClick}
              title={clearButtonText || t('datepicker.clear', 'Clear')}
            >
              <Icon name="close" size="sm" />
            </div>
          )}
        </div>

        {/* Days count */}
        {showDaysCount && value[0] && value[1] && (
          <div className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {`${daysCount} ${t('datepicker.days', 'days')}`}
          </div>
        )}

        {/* Error message */}
        {error && <p className="mt-1 text-sm text-red-500">{error}</p>}

        {/* Helper text */}
        {helperText && !error && (
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{helperText}</p>
        )}

        {/* Calendar dropdown */}
        {isOpen && (
          <FloatingPortal>
            <FloatingFocusManager context={context} modal={false} order={['reference', 'content']}>
              <div
                ref={refs.setFloating}
                style={{
                  ...floatingStyles,
                  width: showTwoMonths
                    ? containerRef.current?.offsetWidth
                      ? containerRef.current.offsetWidth * 1.5
                      : 'auto'
                    : 'auto',
                }}
                className="z-50 datepicker-dropdown shadow-lg rounded-lg overflow-hidden"
                {...getFloatingProps()}
              >
                <div className={`flex ${showTwoMonths ? 'space-x-4' : ''}`}>
                  <Calendar
                    month={calendarMonth}
                    onMonthChange={handleMonthChange}
                    disabledDates={disabledDates}
                    minDate={minDate}
                    maxDate={maxDate}
                    showToday={showToday}
                    showWeekNumbers={showWeekNumbers}
                    firstDayOfWeek={firstDayOfWeek}
                    weekDayNames={weekDayNames}
                    monthNames={monthNames}
                    showTodayButton={showTodayButton}
                    todayButtonText={todayButtonText}
                    rangeMode={true}
                    startDate={value[0]}
                    endDate={value[1]}
                    onRangeSelect={handleRangeSelect}
                  />

                  {showTwoMonths && (
                    <Calendar
                      month={new Date(calendarMonth.getFullYear(), calendarMonth.getMonth() + 1, 1)}
                      onMonthChange={handleMonthChange}
                      disabledDates={disabledDates}
                      minDate={minDate}
                      maxDate={maxDate}
                      showToday={showToday}
                      showWeekNumbers={showWeekNumbers}
                      firstDayOfWeek={firstDayOfWeek}
                      weekDayNames={weekDayNames}
                      monthNames={monthNames}
                      showTodayButton={false}
                      rangeMode={true}
                      startDate={value[0]}
                      endDate={value[1]}
                      onRangeSelect={handleRangeSelect}
                    />
                  )}
                </div>
              </div>
            </FloatingFocusManager>
          </FloatingPortal>
        )}
      </div>
    );
  }
);

RangePicker.displayName = 'RangePicker';

export default RangePicker;
