import React from 'react';
import { useTranslation } from 'react-i18next';

import ModuleCard from '@/modules/components/card/ModuleCard';
import { ResponsiveGrid, Typography } from '@/shared/components/common';

/**
 * Trang chủ module Todolist
 */
const TodolistHomePage: React.FC = () => {
  const { t } = useTranslation(['common', 'todolist']);

  // Danh sách các module con của Todolist
  const subModules = [
    {
      id: 'dashboard',
      title: t('todolist:modules.dashboard.title', 'Dashboard'),
      description: t('todolist:modules.dashboard.description', 'Tổng quan về công việc và dự án'),
      icon: 'chart',
      count: '-',
      countLabel: '',
      linkTo: '/todolist/dashboard',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'tasks',
      title: t('todolist:modules.tasks.title', '<PERSON><PERSON> sách công việc'),
      description: t('todolist:modules.tasks.description', '<PERSON>u<PERSON><PERSON> lý các công việc cần thực hiện'),
      icon: 'check',
      count: 24,
      countLabel: t('todolist:modules.tasks.countLabel', 'Công việc'),
      linkTo: '/todolist/tasks',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'projects',
      title: t('todolist:modules.projects.title', 'Dự án'),
      description: t('todolist:modules.projects.description', 'Quản lý các dự án và tiến độ'),
      icon: 'folder-plus',
      count: 5,
      countLabel: t('todolist:modules.projects.countLabel', 'Dự án'),
      linkTo: '/todolist/projects',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'teams',
      title: t('todolist:modules.teams.title', 'Nhóm'),
      description: t('todolist:modules.teams.description', 'Quản lý các nhóm làm việc'),
      icon: 'users',
      count: 3,
      countLabel: t('todolist:modules.teams.countLabel', 'Nhóm'),
      linkTo: '/todolist/teams',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'statistics',
      title: t('todolist:modules.statistics.title', 'Thống kê'),
      description: t(
        'todolist:modules.statistics.description',
        'Xem báo cáo và thống kê công việc'
      ),
      icon: 'chart',
      count: 4,
      countLabel: t('todolist:modules.statistics.countLabel', 'Báo cáo'),
      linkTo: '/todolist/statistics',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'statistics-enhanced',
      title: t('todolist:modules.statisticsEnhanced.title', 'Thống kê & Báo cáo'),
      description: t(
        'todolist:modules.statisticsEnhanced.description',
        'Biểu đồ và báo cáo nâng cao'
      ),
      icon: 'chart-pie',
      count: 6,
      countLabel: t('todolist:modules.statisticsEnhanced.countLabel', 'Biểu đồ'),
      linkTo: '/todolist/statistics/enhanced',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'employee-gantt',
      title: t('todolist:modules.employeeGantt.title', 'Biểu đồ Gantt nhân viên'),
      description: t(
        'todolist:modules.employeeGantt.description',
        'Xem thời gian hoạt động và thời gian trống của nhân viên'
      ),
      icon: 'calendar',
      count: 3,
      countLabel: t('todolist:modules.employeeGantt.countLabel', 'Nhân viên'),
      linkTo: '/todolist/employee-gantt',
      linkText: t('common:view', 'Xem'),
    },
  ];

  return (
    <div>
      <div className="mb-8">
        <Typography variant="h3" className="mb-2">
          {t('todolist:title', 'Quản lý công việc')}
        </Typography>
        <Typography variant="body1" color="muted">
          {t('todolist:description', 'Quản lý công việc và dự án một cách hiệu quả')}
        </Typography>
      </div>

      <ResponsiveGrid gap={4} maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 4 }}>
        {subModules.map(module => (
          <ModuleCard
            key={module.id}
            title={module.title}
            description={module.description}
            icon={module.icon}
            count={module.count}
            countLabel={module.countLabel}
            linkTo={module.linkTo}
            linkText={module.linkText}
            className="h-full"
          />
        ))}
      </ResponsiveGrid>
    </div>
  );
};

export default TodolistHomePage;
