import React from 'react';
import { useTranslation } from 'react-i18next';

import { Container, Typography } from '@/shared/components/common';
import { IconName } from '@/shared/components/common/Icon/Icon';
import {
  CampaignBuilder,
  ContentPerformanceTracker,
  AudienceSegmentBuilder,
  CustomerSegmentList,
  CustomerSegmentStats,
  CustomerSegmentComparison,
} from '@/shared/components/marketing';

import ComponentDemo from '../components/ComponentDemo';

/**
 * Trang hiển thị các marketing components
 */
const MarketingComponentsPage: React.FC = () => {
  const { t } = useTranslation();

  // Dữ liệu mẫu cho CampaignBuilder
  const campaignSteps = [
    {
      id: '1',
      title: 'Gửi email chào mừng',
      description: 'Gửi email chào mừng khi khách hàng đăng ký',
      type: 'email' as const,
      status: 'active' as const,
    },
    {
      id: '2',
      title: 'Chờ 2 ngày',
      description: 'Chờ 2 ngày sau khi gửi email chào mừng',
      type: 'delay' as const,
      status: 'active' as const,
    },
    {
      id: '3',
      title: 'Kiểm tra mở email',
      description: '<PERSON>ểm tra xem khách hàng đã mở email chưa',
      type: 'condition' as const,
      status: 'active' as const,
      children: {
        yes: [
          {
            id: '4',
            title: 'Gửi email giới thiệu sản phẩm',
            description: 'Gửi email giới thiệu sản phẩm mới',
            type: 'email' as const,
            status: 'active' as const,
          },
        ],
        no: [
          {
            id: '5',
            title: 'Gửi email nhắc nhở',
            description: 'Gửi email nhắc nhở khách hàng',
            type: 'email' as const,
            status: 'active' as const,
          },
        ],
      },
    },
  ];

  // Dữ liệu mẫu cho ContentPerformanceTracker
  const contentItems = [
    {
      id: '1',
      title: 'Hướng dẫn sử dụng sản phẩm XYZ',
      type: 'blog' as const,
      publishDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      status: 'published' as const,
      thumbnail:
        'https://images.unsplash.com/photo-1586953208448-b95a79798f07?q=80&w=1000&auto=format&fit=crop',
      author: {
        id: '1',
        name: 'Nguyễn Văn A',
        avatar:
          'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=100&auto=format&fit=crop',
      },
      metrics: {
        views: 1250,
        engagements: 320,
        conversions: 45,
        conversionRate: 3.6,
        avgTimeOnPage: 180,
        bounceRate: 35,
        shares: 28,
        comments: 15,
        likes: 87,
      },
      comparison: {
        views: 980,
        engagements: 250,
        conversions: 32,
        conversionRate: 3.2,
        avgTimeOnPage: 150,
        bounceRate: 42,
        shares: 20,
        comments: 10,
        likes: 65,
      },
    },
    {
      id: '2',
      title: '10 cách tối ưu hóa hiệu suất kinh doanh',
      type: 'blog' as const,
      publishDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
      status: 'published' as const,
      thumbnail:
        'https://images.unsplash.com/photo-1553877522-43269d4ea984?q=80&w=1000&auto=format&fit=crop',
      author: {
        id: '2',
        name: 'Trần Thị B',
        avatar:
          'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=100&auto=format&fit=crop',
      },
      metrics: {
        views: 2100,
        engagements: 480,
        conversions: 65,
        conversionRate: 3.1,
        avgTimeOnPage: 210,
        bounceRate: 30,
        shares: 45,
        comments: 22,
        likes: 120,
      },
      comparison: {
        views: 1800,
        engagements: 420,
        conversions: 58,
        conversionRate: 3.2,
        avgTimeOnPage: 195,
        bounceRate: 32,
        shares: 38,
        comments: 18,
        likes: 105,
      },
    },
  ];

  const summaryMetrics = [
    {
      name: 'Tổng lượt xem',
      value: 3350,
      change: 20.5,
      icon: 'eye' as IconName,
      color: 'primary',
    },
    {
      name: 'Tổng tương tác',
      value: 800,
      change: 15.2,
      icon: 'activity' as IconName,
      color: 'success',
    },
    {
      name: 'Tổng chuyển đổi',
      value: 110,
      change: 12.8,
      icon: 'shopping-cart' as IconName,
      color: 'info',
    },
    {
      name: 'Tỷ lệ chuyển đổi',
      value: 3.3,
      change: -2.9,
      format: 'percentage',
      icon: 'percent' as IconName,
      color: 'warning',
    },
  ];

  // Dữ liệu mẫu cho CustomerSegmentList
  const customerSegments = [
    {
      id: '1',
      name: 'Khách hàng tiềm năng',
      description: 'Khách hàng đã tương tác với website nhưng chưa mua hàng',
      customerCount: 2500,
      createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      status: 'active' as const,
      type: 'automated' as const,
      tags: ['Tiềm năng', 'Website', 'Mới'],
      icon: 'users' as IconName,
      color: 'blue',
      metrics: {
        openRate: 28.5,
        clickRate: 12.3,
        conversionRate: 3.2,
      },
    },
    {
      id: '2',
      name: 'Khách hàng thân thiết',
      description: 'Khách hàng đã mua hàng nhiều lần trong 6 tháng qua',
      customerCount: 1200,
      createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      status: 'active' as const,
      type: 'manual' as const,
      tags: ['VIP', 'Thân thiết', 'Mua nhiều'],
      icon: 'star' as IconName,
      color: 'yellow',
      metrics: {
        openRate: 45.2,
        clickRate: 22.8,
        conversionRate: 8.5,
      },
    },
    {
      id: '3',
      name: 'Khách hàng không hoạt động',
      description: 'Khách hàng không có hoạt động trong 3 tháng qua',
      customerCount: 3800,
      createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
      status: 'inactive' as const,
      type: 'automated' as const,
      tags: ['Không hoạt động', 'Cần kích hoạt lại'],
      metrics: {
        openRate: 12.1,
        clickRate: 3.5,
        conversionRate: 0.8,
      },
    },
  ];

  // Dữ liệu mẫu cho CustomerSegmentStats
  const segmentStats = [
    {
      name: 'Tổng khách hàng',
      value: 2500,
      change: 15.2,
      icon: 'users' as IconName,
      color: 'primary',
    },
    {
      name: 'Tỷ lệ mở email',
      value: 28.5,
      change: 5.3,
      format: 'percentage',
      icon: 'mail' as IconName,
      color: 'success',
    },
    {
      name: 'Tỷ lệ click',
      value: 12.3,
      change: 2.1,
      format: 'percentage',
      icon: 'mouse-pointer' as IconName,
      color: 'info',
    },
    {
      name: 'Tỷ lệ chuyển đổi',
      value: 3.2,
      change: -0.5,
      format: 'percentage',
      icon: 'shopping-cart' as IconName,
      color: 'warning',
    },
  ];

  const customerBehaviors = [
    {
      name: 'Đã mua hàng trong 30 ngày qua',
      count: 850,
      percentage: 34,
      icon: 'shopping-cart' as IconName,
      color: 'green',
    },
    {
      name: 'Đã xem sản phẩm nhưng chưa mua',
      count: 1200,
      percentage: 48,
      icon: 'eye' as IconName,
      color: 'blue',
    },
    {
      name: 'Đã đăng ký nhận thông báo',
      count: 1850,
      percentage: 74,
      icon: 'bell' as IconName,
      color: 'purple',
    },
    {
      name: 'Đã tham gia sự kiện',
      count: 620,
      percentage: 24.8,
      icon: 'calendar' as IconName,
      color: 'orange',
    },
  ];

  const customerAttributes = [
    {
      name: 'Độ tuổi',
      icon: 'user' as IconName,
      values: [
        { name: '18-24', count: 450, percentage: 18, color: 'blue-500' },
        { name: '25-34', count: 980, percentage: 39.2, color: 'green-500' },
        { name: '35-44', count: 620, percentage: 24.8, color: 'yellow-500' },
        { name: '45+', count: 450, percentage: 18, color: 'red-500' },
      ],
    },
    {
      name: 'Khu vực',
      icon: 'map-pin' as IconName,
      values: [
        { name: 'Hà Nội', count: 850, percentage: 34, color: 'purple-500' },
        { name: 'TP.HCM', count: 950, percentage: 38, color: 'indigo-500' },
        { name: 'Đà Nẵng', count: 320, percentage: 12.8, color: 'cyan-500' },
        { name: 'Khác', count: 380, percentage: 15.2, color: 'gray-500' },
      ],
    },
  ];

  // Dữ liệu mẫu cho CustomerSegmentComparison
  const comparisonMetrics = [
    {
      id: 'openRate',
      name: 'Tỷ lệ mở email',
      format: 'percentage',
      icon: 'mail' as IconName,
      color: 'blue',
      higherIsBetter: true,
    },
    {
      id: 'clickRate',
      name: 'Tỷ lệ click',
      format: 'percentage',
      icon: 'mouse-pointer' as IconName,
      color: 'green',
      higherIsBetter: true,
    },
    {
      id: 'conversionRate',
      name: 'Tỷ lệ chuyển đổi',
      format: 'percentage',
      icon: 'shopping-cart' as IconName,
      color: 'purple',
      higherIsBetter: true,
    },
    {
      id: 'avgOrderValue',
      name: 'Giá trị đơn hàng trung bình',
      format: 'currency',
      icon: 'dollar-sign' as IconName,
      color: 'yellow',
      higherIsBetter: true,
    },
    {
      id: 'churnRate',
      name: 'Tỷ lệ rời bỏ',
      format: 'percentage',
      icon: 'user-x' as IconName,
      color: 'red',
      higherIsBetter: false,
    },
  ];

  const metricValues = [
    { segmentId: '1', metricId: 'openRate', value: 28.5 },
    { segmentId: '1', metricId: 'clickRate', value: 12.3 },
    { segmentId: '1', metricId: 'conversionRate', value: 3.2 },
    { segmentId: '1', metricId: 'avgOrderValue', value: 850000 },
    { segmentId: '1', metricId: 'churnRate', value: 5.2 },

    { segmentId: '2', metricId: 'openRate', value: 45.2 },
    { segmentId: '2', metricId: 'clickRate', value: 22.8 },
    { segmentId: '2', metricId: 'conversionRate', value: 8.5 },
    { segmentId: '2', metricId: 'avgOrderValue', value: 1250000 },
    { segmentId: '2', metricId: 'churnRate', value: 2.1 },

    { segmentId: '3', metricId: 'openRate', value: 12.1 },
    { segmentId: '3', metricId: 'clickRate', value: 3.5 },
    { segmentId: '3', metricId: 'conversionRate', value: 0.8 },
    { segmentId: '3', metricId: 'avgOrderValue', value: 450000 },
    { segmentId: '3', metricId: 'churnRate', value: 12.5 },
  ];

  // Dữ liệu mẫu cho AudienceSegmentBuilder
  const availableFields = [
    {
      id: 'age',
      label: 'Tuổi',
      type: 'number' as const,
      operators: ['equals', 'not_equals', 'greater_than', 'less_than', 'between'] as const,
    },
    {
      id: 'gender',
      label: 'Giới tính',
      type: 'select' as const,
      options: [
        { value: 'male', label: 'Nam' },
        { value: 'female', label: 'Nữ' },
        { value: 'other', label: 'Khác' },
      ],
      operators: ['equals', 'not_equals'] as const,
    },
    {
      id: 'location',
      label: 'Địa điểm',
      type: 'text' as const,
      operators: ['equals', 'not_equals', 'contains', 'not_contains'] as const,
    },
    {
      id: 'interests',
      label: 'Sở thích',
      type: 'multi-select' as const,
      options: [
        { value: 'technology', label: 'Công nghệ' },
        { value: 'fashion', label: 'Thời trang' },
        { value: 'sports', label: 'Thể thao' },
        { value: 'travel', label: 'Du lịch' },
        { value: 'food', label: 'Ẩm thực' },
      ],
      operators: ['in', 'not_in'] as const,
    },
    {
      id: 'purchaseCount',
      label: 'Số lần mua hàng',
      type: 'number' as const,
      operators: ['equals', 'not_equals', 'greater_than', 'less_than', 'between'] as const,
    },
    {
      id: 'lastPurchaseDate',
      label: 'Ngày mua hàng gần nhất',
      type: 'date' as const,
      operators: ['equals', 'not_equals', 'greater_than', 'less_than', 'between'] as const,
    },
    {
      id: 'isSubscribed',
      label: 'Đã đăng ký nhận tin',
      type: 'boolean' as const,
      operators: ['equals'] as const,
    },
  ];

  const rootGroup = {
    id: 'root',
    logic: 'and' as const,
    conditions: [
      {
        id: 'condition-1',
        field: 'age',
        operator: 'between' as const,
        value: [25, 45],
      },
      {
        id: 'condition-2',
        field: 'location',
        operator: 'contains' as const,
        value: 'Hà Nội',
      },
    ],
    groups: [
      {
        id: 'group-1',
        logic: 'or' as const,
        conditions: [
          {
            id: 'condition-3',
            field: 'interests',
            operator: 'in' as const,
            value: ['technology'],
          },
          {
            id: 'condition-4',
            field: 'purchaseCount',
            operator: 'greater_than' as const,
            value: 3,
          },
        ],
      },
    ],
  };

  // Xử lý các actions
  const handleAction = (action: string, data: unknown) => {
    console.log(`${action} action:`, data);
  };

  return (
    <Container>
      <Typography variant="h2" className="mb-6">
        {t('components.marketing.title', 'Marketing Components')}
      </Typography>

      <Typography variant="body1" className="mb-8">
        {t(
          'components.marketing.description',
          'Các components hỗ trợ tính năng marketing và quản lý chiến dịch.'
        )}
      </Typography>

      {/* CampaignBuilder */}
      <ComponentDemo
        title={t('components.marketing.campaignBuilder.title', 'Campaign Builder')}
        description={t(
          'components.marketing.campaignBuilder.description',
          'Giao diện xây dựng và quản lý chiến dịch marketing.'
        )}
        code={`import { CampaignBuilder } from '@/shared/components/marketing';

<CampaignBuilder
  title="Chiến dịch Email Marketing Q2/2023"
  description="Chiến dịch quảng bá sản phẩm mới"
  steps={[
    {
      id: '1',
      title: 'Gửi email chào mừng',
      description: 'Gửi email chào mừng khi khách hàng đăng ký',
      type: 'email',
      status: 'active'
    },
    {
      id: '2',
      title: 'Chờ 2 ngày',
      description: 'Chờ 2 ngày sau khi gửi email chào mừng',
      type: 'delay',
      status: 'active'
    },
    {
      id: '3',
      title: 'Kiểm tra mở email',
      description: 'Kiểm tra xem khách hàng đã mở email chưa',
      type: 'condition',
      status: 'active',
      children: {
        yes: [
          {
            id: '4',
            title: 'Gửi email giới thiệu sản phẩm',
            description: 'Gửi email giới thiệu sản phẩm mới',
            type: 'email',
            status: 'active'
          }
        ],
        no: [
          {
            id: '5',
            title: 'Gửi email nhắc nhở',
            description: 'Gửi email nhắc nhở khách hàng',
            type: 'email',
            status: 'active'
          }
        ]
      }
    }
  ]}
  status="draft"
  onAddStep={(type, parentId, branch) => console.log('Add step', { type, parentId, branch })}
  onRemoveStep={(stepId) => console.log('Remove step', stepId)}
  onUpdateStep={(stepId, data) => console.log('Update step', { stepId, data })}
  onSave={() => console.log('Save campaign')}
  onActivate={() => console.log('Activate campaign')}
/>`}
      >
        <div className="w-full">
          <CampaignBuilder
            title="Chiến dịch Email Marketing Q2/2023"
            description="Chiến dịch quảng bá sản phẩm mới"
            steps={campaignSteps}
            status="draft"
            onAddStep={(type, parentId, branch) =>
              handleAction('addStep', { type, parentId, branch })
            }
            onRemoveStep={stepId => handleAction('removeStep', stepId)}
            onUpdateStep={(stepId, data) => handleAction('updateStep', { stepId, data })}
            onSave={() => handleAction('saveCampaign', {})}
            onActivate={() => handleAction('activateCampaign', {})}
          />
        </div>
      </ComponentDemo>

      {/* ContentPerformanceTracker */}
      <ComponentDemo
        title={t('components.marketing.contentTracker.title', 'Content Performance Tracker')}
        description={t(
          'components.marketing.contentTracker.description',
          'Theo dõi hiệu suất của nội dung marketing.'
        )}
        code={`import { ContentPerformanceTracker } from '@/shared/components/marketing';

<ContentPerformanceTracker
  title="Hiệu suất nội dung Q2/2023"
  contentItems={[
    {
      id: '1',
      title: 'Hướng dẫn sử dụng sản phẩm XYZ',
      type: 'blog',
      publishDate: '2023-04-15',
      status: 'published',
      thumbnail: 'https://example.com/image1.jpg',
      author: {
        id: '1',
        name: 'Nguyễn Văn A',
        avatar: 'https://example.com/avatar1.jpg'
      },
      metrics: {
        views: 1250,
        engagements: 320,
        conversions: 45,
        conversionRate: 3.6
      },
      comparison: {
        views: 980,
        engagements: 250,
        conversions: 32,
        conversionRate: 3.2
      }
    }
  ]}
  summaryMetrics={[
    {
      name: 'Tổng lượt xem',
      value: 3350,
      change: 20.5,
      icon: 'eye',
      color: 'primary'
    },
    {
      name: 'Tổng tương tác',
      value: 800,
      change: 15.2,
      icon: 'activity',
      color: 'success'
    }
  ]}
  timeRangeOptions={[
    { value: 'week', label: 'Tuần này' },
    { value: 'month', label: 'Tháng này' },
    { value: 'quarter', label: 'Quý này' }
  ]}
  onContentClick={(content) => console.log('Content clicked', content)}
  onExport={() => console.log('Export report')}
/>`}
      >
        <div className="w-full">
          <ContentPerformanceTracker
            title="Hiệu suất nội dung Q2/2023"
            contentItems={contentItems}
            summaryMetrics={summaryMetrics}
            timeRangeOptions={[
              { value: 'week', label: 'Tuần này' },
              { value: 'month', label: 'Tháng này' },
              { value: 'quarter', label: 'Quý này' },
            ]}
            onContentClick={content => handleAction('contentClick', content)}
            onExport={() => handleAction('exportReport', {})}
          />
        </div>
      </ComponentDemo>

      {/* AudienceSegmentBuilder */}
      <ComponentDemo
        title={t('components.marketing.audienceBuilder.title', 'Audience Segment Builder')}
        description={t(
          'components.marketing.audienceBuilder.description',
          'Xây dựng và quản lý phân khúc khách hàng.'
        )}
        code={`import { AudienceSegmentBuilder } from '@/shared/components/marketing';

<AudienceSegmentBuilder
  title="Phân khúc khách hàng tiềm năng"
  rootGroup={{
    id: 'root',
    logic: 'and',
    conditions: [
      {
        id: 'condition-1',
        field: 'age',
        operator: 'between',
        value: [25, 45]
      },
      {
        id: 'condition-2',
        field: 'location',
        operator: 'contains',
        value: 'Hà Nội'
      }
    ],
    groups: [
      {
        id: 'group-1',
        logic: 'or',
        conditions: [
          {
            id: 'condition-3',
            field: 'interests',
            operator: 'in',
            value: ['technology']
          },
          {
            id: 'condition-4',
            field: 'purchaseCount',
            operator: 'greater_than',
            value: 3
          }
        ]
      }
    ]
  }}
  availableFields={[
    {
      id: 'age',
      label: 'Tuổi',
      type: 'number',
      operators: ['equals', 'not_equals', 'greater_than', 'less_than', 'between']
    },
    {
      id: 'location',
      label: 'Địa điểm',
      type: 'text',
      operators: ['equals', 'not_equals', 'contains', 'not_contains']
    },
    // Các trường khác...
  ]}
  estimatedAudience={12500}
  onChange={(rootGroup) => console.log('Segment changed', rootGroup)}
  onSave={() => console.log('Save segment')}
  onPreview={() => console.log('Preview audience')}
/>`}
      >
        <div className="w-full">
          <AudienceSegmentBuilder
            title="Phân khúc khách hàng tiềm năng"
            rootGroup={rootGroup}
            availableFields={availableFields}
            estimatedAudience={12500}
            onChange={rootGroup => handleAction('segmentChanged', rootGroup)}
            onSave={() => handleAction('saveSegment', {})}
            onPreview={() => handleAction('previewAudience', {})}
            onRecalculate={() => handleAction('recalculateAudience', {})}
          />
        </div>
      </ComponentDemo>

      {/* CustomerSegmentList */}
      <ComponentDemo
        title={t('components.marketing.customerSegmentList.title', 'Customer Segment List')}
        description={t(
          'components.marketing.customerSegmentList.description',
          'Hiển thị danh sách các phân khúc khách hàng.'
        )}
        code={`import { CustomerSegmentList } from '@/shared/components/marketing';

<CustomerSegmentList
  title="Phân khúc khách hàng"
  segments={[
    {
      id: '1',
      name: 'Khách hàng tiềm năng',
      description: 'Khách hàng đã tương tác với website nhưng chưa mua hàng',
      customerCount: 2500,
      createdAt: '2023-05-15',
      updatedAt: '2023-07-10',
      status: 'active',
      type: 'automated',
      tags: ['Tiềm năng', 'Website', 'Mới'],
      metrics: {
        openRate: 28.5,
        clickRate: 12.3,
        conversionRate: 3.2
      }
    },
    // Các phân khúc khác...
  ]}
  onSegmentClick={(segment) => console.log('Segment clicked', segment)}
  onCreateSegment={() => console.log('Create segment')}
  onDeleteSegment={(segmentId) => console.log('Delete segment', segmentId)}
  showMetrics={true}
  showCustomerCount={true}
  showTags={true}
/>`}
      >
        <div className="w-full">
          <CustomerSegmentList
            title="Phân khúc khách hàng"
            segments={customerSegments}
            onSegmentClick={segment => handleAction('segmentClick', segment)}
            onCreateSegment={() => handleAction('createSegment', {})}
            onDeleteSegment={segmentId => handleAction('deleteSegment', segmentId)}
            onDuplicateSegment={segmentId => handleAction('duplicateSegment', segmentId)}
            onToggleSegmentStatus={(segmentId, status) =>
              handleAction('toggleSegmentStatus', { segmentId, status })
            }
            showMetrics={true}
            showCustomerCount={true}
            showTags={true}
          />
        </div>
      </ComponentDemo>

      {/* CustomerSegmentStats */}
      <ComponentDemo
        title={t('components.marketing.customerSegmentStats.title', 'Customer Segment Stats')}
        description={t(
          'components.marketing.customerSegmentStats.description',
          'Hiển thị thống kê về một phân khúc khách hàng.'
        )}
        code={`import { CustomerSegmentStats } from '@/shared/components/marketing';

<CustomerSegmentStats
  title="Thống kê phân khúc khách hàng"
  segmentName="Khách hàng tiềm năng"
  overviewStats={[
    {
      name: 'Tổng khách hàng',
      value: 2500,
      change: 15.2,
      icon: 'users',
      color: 'primary'
    },
    // Các chỉ số khác...
  ]}
  customerBehaviors={[
    {
      name: 'Đã mua hàng trong 30 ngày qua',
      count: 850,
      percentage: 34,
      icon: 'shopping-cart',
      color: 'green'
    },
    // Các hành vi khác...
  ]}
  timeRangeOptions={[
    { value: 'week', label: 'Tuần này' },
    { value: 'month', label: 'Tháng này' },
    { value: 'quarter', label: 'Quý này' }
  ]}
  onTimeRangeChange={(timeRange) => console.log('Time range changed', timeRange)}
  onExport={() => console.log('Export report')}
/>`}
      >
        <div className="w-full">
          <CustomerSegmentStats
            title="Thống kê phân khúc khách hàng"
            segmentName="Khách hàng tiềm năng"
            overviewStats={segmentStats}
            customerBehaviors={customerBehaviors}
            customerAttributes={customerAttributes}
            timeRangeOptions={[
              { value: 'week', label: 'Tuần này' },
              { value: 'month', label: 'Tháng này' },
              { value: 'quarter', label: 'Quý này' },
            ]}
            onTimeRangeChange={timeRange => handleAction('timeRangeChange', timeRange)}
            onExport={() => handleAction('exportReport', {})}
          />
        </div>
      </ComponentDemo>

      {/* CustomerSegmentComparison */}
      <ComponentDemo
        title={t(
          'components.marketing.customerSegmentComparison.title',
          'Customer Segment Comparison'
        )}
        description={t(
          'components.marketing.customerSegmentComparison.description',
          'So sánh các phân khúc khách hàng với nhau.'
        )}
        code={`import { CustomerSegmentComparison } from '@/shared/components/marketing';

<CustomerSegmentComparison
  title="So sánh phân khúc khách hàng"
  segments={[
    {
      id: '1',
      name: 'Khách hàng tiềm năng',
      customerCount: 2500,
      // Các thuộc tính khác...
    },
    // Các phân khúc khác...
  ]}
  selectedSegmentIds={['1', '2']}
  metrics={[
    {
      id: 'openRate',
      name: 'Tỷ lệ mở email',
      format: 'percentage',
      icon: 'mail',
      color: 'blue',
      higherIsBetter: true
    },
    // Các chỉ số khác...
  ]}
  metricValues={[
    { segmentId: '1', metricId: 'openRate', value: 28.5 },
    // Các giá trị khác...
  ]}
  onSegmentSelectionChange={(segmentIds) => console.log('Segment selection changed', segmentIds)}
  onExport={() => console.log('Export report')}
  maxSegments={4}
/>`}
      >
        <div className="w-full">
          <CustomerSegmentComparison
            title="So sánh phân khúc khách hàng"
            segments={customerSegments}
            selectedSegmentIds={['1', '2']}
            metrics={comparisonMetrics}
            metricValues={metricValues}
            onSegmentSelectionChange={segmentIds =>
              handleAction('segmentSelectionChange', segmentIds)
            }
            onExport={() => handleAction('exportReport', {})}
            maxSegments={3}
          />
        </div>
      </ComponentDemo>

      {/* EmailTemplateEditor - Đã có sẵn */}
      <ComponentDemo
        title={t('components.marketing.emailEditor.title', 'Email Template Editor')}
        description={t(
          'components.marketing.emailEditor.description',
          'Trình soạn thảo mẫu email marketing.'
        )}
        code={`// EmailTemplateEditor đã có sẵn trong dự án
// Vui lòng sử dụng component có sẵn`}
      >
        <div className="w-full p-6 bg-gray-100 dark:bg-gray-800 rounded-lg text-center">
          <Typography variant="body1" className="text-gray-500 dark:text-gray-400">
            {t('marketing.emailEditor.available', 'Component đã có sẵn trong dự án')}
          </Typography>
        </div>
      </ComponentDemo>
    </Container>
  );
};

export default MarketingComponentsPage;
