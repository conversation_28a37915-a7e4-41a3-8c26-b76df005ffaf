import React from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import ComponentDemo from '@/modules/components/components/ComponentDemo';
import {
  Card,
  Container,
  Typography,
  FormItem,
  Input,
  Textarea,
  Checkbox,
} from '@/shared/components/common';
import { FormWizard, StepIndicator, StepNavigation } from '@/shared/components/form';

/**
 * Trang demo cho FormWizard component
 */
const FormWizardPage: React.FC = () => {
  const { t } = useTranslation();

  // Schema validation cho bước 1
  const personalInfoSchema = z.object({
    firstName: z.string().min(1, 'Họ không được để trống'),
    lastName: z.string().min(1, 'Tên không được để trống'),
    email: z.string().email('Email không hợp lệ').min(1, '<PERSON>ail không được để trống'),
    phone: z.string().min(1, '<PERSON><PERSON> điện thoại không được để trống'),
  });

  // Schema validation cho bước 2
  const addressSchema = z.object({
    address: z.string().min(1, 'Địa chỉ không được để trống'),
    city: z.string().min(1, 'Thành phố không được để trống'),
    state: z.string().min(1, 'Tỉnh/Thành phố không được để trống'),
    zipCode: z.string().min(1, 'Mã bưu điện không được để trống'),
  });

  // Schema validation cho bước 3
  const accountSchema = z
    .object({
      username: z.string().min(1, 'Tên đăng nhập không được để trống'),
      password: z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
      confirmPassword: z.string().min(1, 'Xác nhận mật khẩu không được để trống'),
      agreeTerms: z.boolean().refine(val => val === true, {
        message: 'Bạn phải đồng ý với điều khoản và điều kiện',
      }),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: 'Mật khẩu và xác nhận mật khẩu không khớp',
      path: ['confirmPassword'],
    });

  // Xử lý khi submit form
  const handleSubmit = (data: Record<string, unknown>) => {
    console.log('Form submitted:', data);
    alert('Form submitted successfully!');
  };

  return (
    <Container>
      <Typography variant="h4" className="mb-6">
        {t('components.form.wizard.title', 'Form Wizard')}
      </Typography>

      <Typography variant="body1" className="mb-6">
        {t(
          'components.form.wizard.description',
          'FormWizard component quản lý form nhiều bước với validation và navigation.'
        )}
      </Typography>

      <div className="space-y-8">
        {/* Basic Form Wizard */}
        <ComponentDemo
          title={t('components.form.wizard.basic.title', 'Basic Form Wizard')}
          description={t(
            'components.form.wizard.basic.description',
            'Form wizard cơ bản với 3 bước và validation.'
          )}
          code={`import { FormWizard, FormStep, StepIndicator, StepNavigation } from '@/shared/components/form';
import { z } from 'zod';

// Schema validation cho các bước
const personalInfoSchema = z.object({
  firstName: z.string().min(1, 'Họ không được để trống'),
  lastName: z.string().min(1, 'Tên không được để trống'),
  email: z.string().email('Email không hợp lệ').min(1, 'Email không được để trống'),
  phone: z.string().min(1, 'Số điện thoại không được để trống'),
});

const addressSchema = z.object({
  address: z.string().min(1, 'Địa chỉ không được để trống'),
  city: z.string().min(1, 'Thành phố không được để trống'),
  state: z.string().min(1, 'Tỉnh/Thành phố không được để trống'),
  zipCode: z.string().min(1, 'Mã bưu điện không được để trống'),
});

const accountSchema = z.object({
  username: z.string().min(1, 'Tên đăng nhập không được để trống'),
  password: z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
  confirmPassword: z.string().min(1, 'Xác nhận mật khẩu không được để trống'),
  agreeTerms: z.boolean().refine(val => val === true, {
    message: 'Bạn phải đồng ý với điều khoản và điều kiện',
  }),
}).refine(data => data.password === data.confirmPassword, {
  message: 'Mật khẩu và xác nhận mật khẩu không khớp',
  path: ['confirmPassword'],
});

// Xử lý khi submit form
const handleSubmit = (data) => {
  console.log('Form submitted:', data);
  alert('Form submitted successfully!');
};

<FormWizard
  steps={[
    {
      id: 'personal',
      title: 'Thông tin cá nhân',
      validationSchema: personalInfoSchema,
      content: ({ control }) => (
        <div className="space-y-4">
          <FormItem name="firstName" label="Họ">
            <Input />
          </FormItem>
          <FormItem name="lastName" label="Tên">
            <Input />
          </FormItem>
          <FormItem name="email" label="Email">
            <Input type="email" />
          </FormItem>
          <FormItem name="phone" label="Số điện thoại">
            <Input type="tel" />
          </FormItem>
        </div>
      ),
    },
    {
      id: 'address',
      title: 'Địa chỉ',
      validationSchema: addressSchema,
      content: ({ control }) => (
        <div className="space-y-4">
          <FormItem name="address" label="Địa chỉ">
            <Textarea rows={3} />
          </FormItem>
          <FormItem name="city" label="Thành phố">
            <Input />
          </FormItem>
          <FormItem name="state" label="Tỉnh/Thành phố">
            <Input />
          </FormItem>
          <FormItem name="zipCode" label="Mã bưu điện">
            <Input />
          </FormItem>
        </div>
      ),
    },
    {
      id: 'account',
      title: 'Tài khoản',
      validationSchema: accountSchema,
      content: ({ control }) => (
        <div className="space-y-4">
          <FormItem name="username" label="Tên đăng nhập">
            <Input />
          </FormItem>
          <FormItem name="password" label="Mật khẩu">
            <Input type="password" />
          </FormItem>
          <FormItem name="confirmPassword" label="Xác nhận mật khẩu">
            <Input type="password" />
          </FormItem>
          <FormItem name="agreeTerms">
            <Checkbox label="Tôi đồng ý với điều khoản và điều kiện" />
          </FormItem>
        </div>
      ),
    },
  ]}
  initialValues={{
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    username: '',
    password: '',
    confirmPassword: '',
    agreeTerms: false,
  }}
  onSubmit={handleSubmit}
  validateOnStepChange
>
  {({ steps, currentStep, currentStepIndex, stepStatus, formMethods, goToNextStep, goToPreviousStep, isFirstStep, isLastStep, submitForm }) => (
    <>
      <StepIndicator
        steps={steps}
        currentStepId={currentStep.id}
        stepStatus={stepStatus}
        orientation="horizontal"
        showTitle
        showDescription={false}
        className="mb-6"
      />

      <Card className="p-6 mb-6">
        {currentStep.content(formMethods)}
      </Card>

      <StepNavigation
        currentStep={currentStepIndex}
        totalSteps={steps.length}
        onNext={goToNextStep}
        onPrevious={goToPreviousStep}
        onSubmit={submitForm}
        isPreviousDisabled={isFirstStep}
        align="between"
      />
    </>
  )}
</FormWizard>`}
        >
          <Card className="p-6">
            <FormWizard
              steps={[
                {
                  id: 'personal',
                  title: 'Thông tin cá nhân',
                  validationSchema: personalInfoSchema,
                  content: () => (
                    <div className="space-y-4">
                      <FormItem name="firstName" label="Họ">
                        <Input />
                      </FormItem>
                      <FormItem name="lastName" label="Tên">
                        <Input />
                      </FormItem>
                      <FormItem name="email" label="Email">
                        <Input type="email" />
                      </FormItem>
                      <FormItem name="phone" label="Số điện thoại">
                        <Input type="tel" />
                      </FormItem>
                    </div>
                  ),
                },
                {
                  id: 'address',
                  title: 'Địa chỉ',
                  validationSchema: addressSchema,
                  content: () => (
                    <div className="space-y-4">
                      <FormItem name="address" label="Địa chỉ">
                        <Textarea rows={3} />
                      </FormItem>
                      <FormItem name="city" label="Thành phố">
                        <Input />
                      </FormItem>
                      <FormItem name="state" label="Tỉnh/Thành phố">
                        <Input />
                      </FormItem>
                      <FormItem name="zipCode" label="Mã bưu điện">
                        <Input />
                      </FormItem>
                    </div>
                  ),
                },
                {
                  id: 'account',
                  title: 'Tài khoản',
                  validationSchema: accountSchema,
                  content: () => (
                    <div className="space-y-4">
                      <FormItem name="username" label="Tên đăng nhập">
                        <Input />
                      </FormItem>
                      <FormItem name="password" label="Mật khẩu">
                        <Input type="password" />
                      </FormItem>
                      <FormItem name="confirmPassword" label="Xác nhận mật khẩu">
                        <Input type="password" />
                      </FormItem>
                      <FormItem name="agreeTerms">
                        <Checkbox label="Tôi đồng ý với điều khoản và điều kiện" />
                      </FormItem>
                    </div>
                  ),
                },
              ]}
              initialValues={{
                firstName: '',
                lastName: '',
                email: '',
                phone: '',
                address: '',
                city: '',
                state: '',
                zipCode: '',
                username: '',
                password: '',
                confirmPassword: '',
                agreeTerms: false,
              }}
              onSubmit={handleSubmit}
              validateOnStepChange
            >
              {({
                steps,
                currentStep,
                currentStepIndex,
                stepStatus,
                formMethods,
                goToNextStep,
                goToPreviousStep,
                isFirstStep,

                submitForm,
              }) => (
                <>
                  <StepIndicator
                    steps={steps}
                    currentStepId={currentStep.id}
                    stepStatus={stepStatus}
                    orientation="horizontal"
                    showTitle
                    showDescription={false}
                    className="mb-6"
                  />

                  <Card className="p-6 mb-6">
                    {typeof currentStep.content === 'function'
                      ? currentStep.content(formMethods)
                      : currentStep.content}
                  </Card>

                  <StepNavigation
                    currentStep={currentStepIndex}
                    totalSteps={steps.length}
                    onNext={goToNextStep}
                    onPrevious={goToPreviousStep}
                    onSubmit={submitForm}
                    isPreviousDisabled={isFirstStep}
                    align="between"
                  />
                </>
              )}
            </FormWizard>
          </Card>
        </ComponentDemo>
      </div>
    </Container>
  );
};

export default FormWizardPage;
