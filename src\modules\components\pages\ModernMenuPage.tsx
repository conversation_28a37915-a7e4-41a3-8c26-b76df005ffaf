import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, ModernMenu, IconCard } from '@/shared/components/common';

import ComponentDemo from '../components/ComponentDemo';

const ModernMenuPage: React.FC = () => {
  const { t } = useTranslation();
  const [showBasicMenu, setShowBasicMenu] = useState(false);
  const [showIconMenu, setShowIconMenu] = useState(false);
  const [showPlacementMenu, setShowPlacementMenu] = useState(false);
  const [placement, setPlacement] = useState<'top' | 'right' | 'bottom' | 'left'>('bottom');

  return (
    <div className="p-4 sm:p-6 space-y-8">
      <div>
        <h1 className="text-2xl font-bold mb-4 text-foreground">
          {t('components.modernMenu.title', 'Modern Menu')}
        </h1>
        <p className="text-muted mb-6">
          {t(
            'components.modernMenu.description',
            'Modern menu component with various styles and placements.'
          )}
        </p>
      </div>

      {/* Basic Menu */}
      <ComponentDemo
        title={t('components.modernMenu.basic.title', 'Basic Menu')}
        description={t(
          'components.modernMenu.basic.description',
          'Basic menu with default settings.'
        )}
      >
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Button onClick={() => setShowBasicMenu(!showBasicMenu)}>
              {t('common.openMenu', 'Open Menu')}
            </Button>

            {showBasicMenu && (
              <ModernMenu
                isOpen={showBasicMenu}
                onClose={() => setShowBasicMenu(false)}
                items={[
                  {
                    id: 'item1',
                    label: t('common.edit', 'Edit'),
                    onClick: () => console.log('Edit clicked'),
                  },
                  {
                    id: 'item2',
                    label: t('common.delete', 'Delete'),
                    onClick: () => console.log('Delete clicked'),
                  },
                  {
                    id: 'divider1',
                    divider: true,
                  },
                  {
                    id: 'item3',
                    label: t('common.share', 'Share'),
                    onClick: () => console.log('Share clicked'),
                  },
                ]}
              />
            )}
          </div>
        </div>
      </ComponentDemo>

      {/* Menu with Icons */}
      <ComponentDemo
        title={t('components.modernMenu.withIcons.title', 'Menu with Icons')}
        description={t('components.modernMenu.withIcons.description', 'Menu items with icons.')}
      >
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Button onClick={() => setShowIconMenu(!showIconMenu)}>
              {t('common.openMenu', 'Open Menu')}
            </Button>

            {showIconMenu && (
              <ModernMenu
                isOpen={showIconMenu}
                onClose={() => setShowIconMenu(false)}
                items={[
                  {
                    id: 'item1',
                    label: t('common.edit', 'Edit'),
                    icon: 'edit',
                    onClick: () => console.log('Edit clicked'),
                  },
                  {
                    id: 'item2',
                    label: t('common.delete', 'Delete'),
                    icon: 'trash',
                    onClick: () => console.log('Delete clicked'),
                  },
                  {
                    id: 'divider1',
                    divider: true,
                  },
                  {
                    id: 'item3',
                    label: t('common.share', 'Share'),
                    icon: 'share',
                    onClick: () => console.log('Share clicked'),
                  },
                  {
                    id: 'item4',
                    label: t('common.download', 'Download'),
                    icon: 'download',
                    onClick: () => console.log('Download clicked'),
                  },
                ]}
              />
            )}
          </div>
        </div>
      </ComponentDemo>

      {/* Menu Placement */}
      <ComponentDemo
        title={t('components.modernMenu.placement.title', 'Menu Placement')}
        description={t(
          'components.modernMenu.placement.description',
          'Menu with different placements.'
        )}
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <Button
              variant={placement === 'top' ? 'primary' : 'outline'}
              onClick={() => setPlacement('top')}
            >
              {t('components.modernMenu.placement.top', 'Top')}
            </Button>
            <Button
              variant={placement === 'right' ? 'primary' : 'outline'}
              onClick={() => setPlacement('right')}
            >
              {t('components.modernMenu.placement.right', 'Right')}
            </Button>
            <Button
              variant={placement === 'bottom' ? 'primary' : 'outline'}
              onClick={() => setPlacement('bottom')}
            >
              {t('components.modernMenu.placement.bottom', 'Bottom')}
            </Button>
            <Button
              variant={placement === 'left' ? 'primary' : 'outline'}
              onClick={() => setPlacement('left')}
            >
              {t('components.modernMenu.placement.left', 'Left')}
            </Button>
          </div>

          <div className="flex items-center justify-center p-16 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
            <div className="relative">
              <IconCard icon="menu" onClick={() => setShowPlacementMenu(!showPlacementMenu)} />

              {showPlacementMenu && (
                <ModernMenu
                  isOpen={showPlacementMenu}
                  onClose={() => setShowPlacementMenu(false)}
                  placement={placement}
                  items={[
                    {
                      id: 'item1',
                      label: t('common.edit', 'Edit'),
                      icon: 'edit',
                      onClick: () => console.log('Edit clicked'),
                    },
                    {
                      id: 'item2',
                      label: t('common.delete', 'Delete'),
                      icon: 'trash',
                      onClick: () => console.log('Delete clicked'),
                    },
                  ]}
                />
              )}
            </div>
          </div>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default ModernMenuPage;
