import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Typography, Icon, Card } from '@/shared/components/common';
import {
  AsyncSelect,
  CreatableSelect,
  ComboboxSelect,
  TypeaheadSelect,
} from '@/shared/components/common/Select';

import { ComponentDemo } from '../../components';

import type { SelectOption } from '@/shared/components/common/Select/Select';

/**
 * Trang demo cho các component Select nâng cao
 */
const AdvancedSelectDemoNew: React.FC = () => {
  const { t } = useTranslation();
  const [asyncValue, setAsyncValue] = useState<string>('');
  const [creatableValue, setCreatableValue] = useState<string>('');
  const [comboboxValue, setComboboxValue] = useState<string>('');
  const [typeaheadValue, setTypeaheadValue] = useState<string>('');
  const [typeaheadMultiValue, setTypeaheadMultiValue] = useState<string[]>([]);

  // Options cho CreatableSelect
  const creatableOptions: SelectOption[] = [
    { value: 'chocolate', label: 'Chocolate' },
    { value: 'strawberry', label: 'Strawberry' },
    { value: 'vanilla', label: 'Vanilla' },
  ];

  // Options cho ComboboxSelect
  const comboboxOptions: SelectOption[] = [
    { value: 'red', label: 'Red' },
    { value: 'green', label: 'Green' },
    { value: 'blue', label: 'Blue' },
    { value: 'yellow', label: 'Yellow' },
    { value: 'purple', label: 'Purple' },
  ];

  // Options cho TypeaheadSelect
  const typeaheadOptions: SelectOption[] = [
    { value: 'javascript', label: 'JavaScript', icon: <Icon name="code" size="sm" /> },
    { value: 'typescript', label: 'TypeScript', icon: <Icon name="code" size="sm" /> },
    { value: 'python', label: 'Python', icon: <Icon name="code" size="sm" /> },
    { value: 'java', label: 'Java', icon: <Icon name="code" size="sm" /> },
    { value: 'csharp', label: 'C#', icon: <Icon name="code" size="sm" /> },
    { value: 'php', label: 'PHP', icon: <Icon name="code" size="sm" /> },
    { value: 'ruby', label: 'Ruby', icon: <Icon name="code" size="sm" /> },
    { value: 'swift', label: 'Swift', icon: <Icon name="code" size="sm" /> },
    { value: 'kotlin', label: 'Kotlin', icon: <Icon name="code" size="sm" /> },
    { value: 'go', label: 'Go', icon: <Icon name="code" size="sm" /> },
  ];

  // Simulate API call for AsyncSelect
  const loadOptions = async (inputValue: string): Promise<SelectOption[]> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock API response
    const mockData = [
      { value: 'new-york', label: 'New York' },
      { value: 'london', label: 'London' },
      { value: 'paris', label: 'Paris' },
      { value: 'tokyo', label: 'Tokyo' },
      { value: 'sydney', label: 'Sydney' },
      { value: 'berlin', label: 'Berlin' },
      { value: 'rome', label: 'Rome' },
      { value: 'madrid', label: 'Madrid' },
      { value: 'amsterdam', label: 'Amsterdam' },
      { value: 'moscow', label: 'Moscow' },
    ];

    // Filter based on input
    return mockData.filter(item => item.label.toLowerCase().includes(inputValue.toLowerCase()));
  };

  return (
    <div className="container mx-auto p-4 sm:p-6">
      <div className="mb-8">
        <Typography variant="h1" className="mb-2">
          Advanced Select Components
        </Typography>
        <Typography className="text-gray-600 dark:text-gray-400">
          A collection of advanced select components with various features for different use cases.
        </Typography>
      </div>

      {/* Overview Card */}
      <Card title="Advanced Select Components Overview" className="mb-6">
        <p className="mb-4">RedAI Frontend Template cung cấp các component Select nâng cao sau:</p>
        <ul className="list-disc list-inside space-y-1 pl-4 mb-4">
          <li>
            <strong>AsyncSelect</strong>: Select với khả năng tải dữ liệu từ API
          </li>
          <li>
            <strong>CreatableSelect</strong>: Select cho phép tạo option mới nếu không tìm thấy
          </li>
          <li>
            <strong>ComboboxSelect</strong>: Select kết hợp giữa dropdown và input, cho phép nhập tự
            do
          </li>
          <li>
            <strong>TypeaheadSelect</strong>: Select với gợi ý khi gõ
          </li>
        </ul>
        <p className="mb-2">
          <strong>Các ví dụ dưới đây minh họa cách sử dụng các component Select nâng cao:</strong>
        </p>
      </Card>

      {/* AsyncSelect */}
      <ComponentDemo
        title={t('components.inputs.asyncSelect.title', 'Async Select')}
        description={t(
          'components.inputs.asyncSelect.description',
          'Select with ability to load data from API'
        )}
        code={`import { AsyncSelect } from '@/shared/components/common/Select';

// Function to load options from API
const loadOptions = async (inputValue: string): Promise<SelectOption[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Mock API response
  const mockData = [
    { value: 'new-york', label: 'New York' },
    { value: 'london', label: 'London' },
    // ... more cities
  ];

  // Filter based on input
  return mockData.filter(item =>
    item.label.toLowerCase().includes(inputValue.toLowerCase())
  );
};

// State
const [value, setValue] = useState('');

// Render
<AsyncSelect
  label="Search for a city"
  value={value}
  onChange={(val) => setValue(val as string)}
  loadOptions={loadOptions}
  placeholder="Type to search cities..."
  debounceTime={300}
  noOptionsMessage="No cities found"
  loadingMessage="Searching cities..."
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <AsyncSelect
            label="Search for a city"
            value={asyncValue}
            onChange={val => setAsyncValue(val as string)}
            loadOptions={loadOptions}
            placeholder="Type to search cities..."
            debounceTime={300}
            noOptionsMessage="No cities found"
            loadingMessage="Searching cities..."
          />
          {asyncValue && (
            <div className="mt-2 text-sm">
              Selected city: <span className="font-medium">{asyncValue}</span>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* CreatableSelect */}
      <ComponentDemo
        title={t('components.inputs.creatableSelect.title', 'Creatable Select')}
        description={t(
          'components.inputs.creatableSelect.description',
          'Select that allows creating new options if not found'
        )}
        code={`import { CreatableSelect } from '@/shared/components/common/Select';

// Options
const options = [
  { value: 'chocolate', label: 'Chocolate' },
  { value: 'strawberry', label: 'Strawberry' },
  { value: 'vanilla', label: 'Vanilla' },
];

// State
const [value, setValue] = useState('');

// Render
<CreatableSelect
  label="Select or create a flavor"
  value={value}
  onChange={(val) => setValue(val as string)}
  options={options}
  placeholder="Choose or create a flavor..."
  formatCreateLabel={(inputValue) => \`Create "\${inputValue}"\`}
  onCreateOption={(inputValue) => console.log(\`Created: \${inputValue}\`)}
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <CreatableSelect
            label="Select or create a flavor"
            value={creatableValue}
            onChange={val => setCreatableValue(val as string)}
            options={creatableOptions}
            placeholder="Choose or create a flavor..."
            formatCreateLabel={inputValue => `Create "${inputValue}"`}
            onCreateOption={inputValue => console.log(`Created: ${inputValue}`)}
          />
          {creatableValue && (
            <div className="mt-2 text-sm">
              Selected flavor: <span className="font-medium">{creatableValue}</span>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* ComboboxSelect */}
      <ComponentDemo
        title={t('components.inputs.comboboxSelect.title', 'Combobox Select')}
        description={t(
          'components.inputs.comboboxSelect.description',
          'Select that combines dropdown and input, allowing free text entry'
        )}
        code={`import { ComboboxSelect } from '@/shared/components/common/Select';

// Options
const options = [
  { value: 'red', label: 'Red' },
  { value: 'green', label: 'Green' },
  { value: 'blue', label: 'Blue' },
  { value: 'yellow', label: 'Yellow' },
  { value: 'purple', label: 'Purple' },
];

// State
const [value, setValue] = useState('');

// Render
<ComboboxSelect
  label="Select or type a color"
  value={value}
  onChange={(val) => setValue(val)}
  options={options}
  placeholder="Choose or type a color..."
  allowCustomValue={true}
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <ComboboxSelect
            label="Select or type a color"
            value={comboboxValue}
            onChange={val => setComboboxValue(val)}
            options={comboboxOptions}
            placeholder="Choose or type a color..."
            allowCustomValue={true}
          />
          {comboboxValue && (
            <div className="mt-2 text-sm">
              Selected/entered color: <span className="font-medium">{comboboxValue}</span>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* TypeaheadSelect */}
      <ComponentDemo
        title={t('components.inputs.typeaheadSelect.title', 'Typeahead Select')}
        description={t(
          'components.inputs.typeaheadSelect.description',
          'Select with suggestions as you type'
        )}
        code={`import { TypeaheadSelect } from '@/shared/components/common/Select';
import { Icon } from '@/shared/components/common';

// Options
const options = [
  { value: 'javascript', label: 'JavaScript', icon: <Icon name="code" size="sm" /> },
  { value: 'typescript', label: 'TypeScript', icon: <Icon name="code" size="sm" /> },
  // ... more programming languages
];

// State
const [value, setValue] = useState('');

// Render
<TypeaheadSelect
  label="Select a programming language"
  value={value}
  onChange={(val) => setValue(val as string)}
  options={options}
  placeholder="Type to search..."
  highlightMatch={true}
  maxSuggestions={5}
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <TypeaheadSelect
            label="Select a programming language"
            value={typeaheadValue}
            onChange={val => setTypeaheadValue(val as string)}
            options={typeaheadOptions}
            placeholder="Type to search..."
            highlightMatch={true}
            maxSuggestions={5}
          />
          {typeaheadValue && (
            <div className="mt-2 text-sm">
              Selected language: <span className="font-medium">{typeaheadValue}</span>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* TypeaheadSelect (Multiple) */}
      <ComponentDemo
        title="Multiple Typeahead Select"
        description="Typeahead Select with multiple selection"
        code={`import { TypeaheadSelect } from '@/shared/components/common/Select';

// State
const [value, setValue] = useState<string[]>([]);

// Render
<TypeaheadSelect
  label="Select multiple programming languages"
  value={value}
  onChange={(val) => setValue(val as string[])}
  options={options}
  placeholder="Type to search..."
  multiple={true}
  highlightMatch={true}
  maxSuggestions={5}
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <TypeaheadSelect
            label="Select multiple programming languages"
            value={typeaheadMultiValue}
            onChange={val => setTypeaheadMultiValue(val as string[])}
            options={typeaheadOptions}
            placeholder="Type to search..."
            multiple={true}
            highlightMatch={true}
            maxSuggestions={5}
          />
          {typeaheadMultiValue.length > 0 && (
            <div className="mt-2 text-sm">
              Selected languages:{' '}
              <span className="font-medium">{typeaheadMultiValue.join(', ')}</span>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* Hướng dẫn sử dụng */}
      <Card title="Hướng dẫn sử dụng Advanced Select Components" className="mb-6">
        <div className="space-y-4">
          <p className="font-medium">Chọn đúng component Select cho từng trường hợp sử dụng:</p>
          <table className="min-w-full mt-2 border-collapse">
            <thead>
              <tr className="bg-gray-100 dark:bg-gray-800">
                <th className="p-2 text-left border border-gray-300 dark:border-gray-700">
                  Component
                </th>
                <th className="p-2 text-left border border-gray-300 dark:border-gray-700">
                  Trường hợp sử dụng
                </th>
                <th className="p-2 text-left border border-gray-300 dark:border-gray-700">Ví dụ</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border border-gray-300 dark:border-gray-700">
                  <code>AsyncSelect</code>
                </td>
                <td className="p-2 border border-gray-300 dark:border-gray-700">
                  Khi cần tải dữ liệu từ API dựa trên input của người dùng
                </td>
                <td className="p-2 border border-gray-300 dark:border-gray-700">
                  Tìm kiếm thành phố, sản phẩm, người dùng
                </td>
              </tr>
              <tr>
                <td className="p-2 border border-gray-300 dark:border-gray-700">
                  <code>CreatableSelect</code>
                </td>
                <td className="p-2 border border-gray-300 dark:border-gray-700">
                  Khi muốn cho phép người dùng tạo options mới
                </td>
                <td className="p-2 border border-gray-300 dark:border-gray-700">
                  Tags, labels, categories
                </td>
              </tr>
              <tr>
                <td className="p-2 border border-gray-300 dark:border-gray-700">
                  <code>ComboboxSelect</code>
                </td>
                <td className="p-2 border border-gray-300 dark:border-gray-700">
                  Khi muốn cho phép người dùng nhập tự do
                </td>
                <td className="p-2 border border-gray-300 dark:border-gray-700">
                  Nhập màu sắc, kích thước, số lượng
                </td>
              </tr>
              <tr>
                <td className="p-2 border border-gray-300 dark:border-gray-700">
                  <code>TypeaheadSelect</code>
                </td>
                <td className="p-2 border border-gray-300 dark:border-gray-700">
                  Khi cần gợi ý khi gõ và hỗ trợ chọn nhiều
                </td>
                <td className="p-2 border border-gray-300 dark:border-gray-700">
                  Chọn kỹ năng, ngôn ngữ, công nghệ
                </td>
              </tr>
            </tbody>
          </table>

          <div className="mt-6">
            <p className="font-medium">Các bước sử dụng AsyncSelect:</p>
            <ol className="list-decimal list-inside space-y-2 pl-4">
              <li>
                Import component:{' '}
                <code>
                  import &#123; AsyncSelect &#125; from '@/shared/components/common/Select';
                </code>
              </li>
              <li>
                Định nghĩa hàm loadOptions:
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded mt-1 text-sm overflow-auto">
                  {`const loadOptions = async (inputValue: string): Promise<SelectOption[]> => {
  // Gọi API với inputValue
  const response = await fetch(\`/api/search?q=\${inputValue}\`);
  const data = await response.json();

  // Chuyển đổi dữ liệu thành SelectOption[]
  return data.map(item => ({
    value: item.id,
    label: item.name
  }));
};`}
                </pre>
              </li>
              <li>
                Render component:
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded mt-1 text-sm overflow-auto">
                  {`<AsyncSelect
  label="Search"
  value={value}
  onChange={(val) => setValue(val as string)}
  loadOptions={loadOptions}
  placeholder="Type to search..."
  debounceTime={300}
  noOptionsMessage="No results found"
  loadingMessage="Searching..."
/>`}
                </pre>
              </li>
            </ol>
          </div>

          <div className="mt-6">
            <p className="font-medium">Best Practices:</p>
            <ul className="list-disc list-inside space-y-1 pl-4">
              <li>
                Sử dụng <code>debounceTime</code> cho AsyncSelect để tránh gọi API quá nhiều
              </li>
              <li>Hiển thị trạng thái loading và thông báo khi không có kết quả</li>
              <li>
                Sử dụng <code>formatCreateLabel</code> để tùy chỉnh label khi tạo option mới
              </li>
              <li>
                Sử dụng <code>highlightMatch</code> để highlight text khớp với từ khóa tìm kiếm
              </li>
              <li>Xử lý lỗi khi gọi API trong AsyncSelect</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* Tài liệu tham khảo */}
      <Card title="Tài liệu tham khảo" className="mb-6">
        <p className="mb-4">
          Để biết thêm chi tiết về cách sử dụng các component Select nâng cao, vui lòng tham khảo:
        </p>
        <ul className="list-disc list-inside space-y-1 pl-4">
          <li>
            Tài liệu hướng dẫn: <code>docs/guides/advanced-select-components.md</code>
          </li>
          <li>
            Ví dụ thực tế: <code>src/modules/components/pages/form/SelectUsageDemo.tsx</code>
          </li>
          <li>
            Mã nguồn: <code>src/shared/components/common/Select/</code>
          </li>
        </ul>
      </Card>
    </div>
  );
};

export default AdvancedSelectDemoNew;
