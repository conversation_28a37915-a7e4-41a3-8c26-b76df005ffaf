import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import EventForm from '@/modules/calendar/components/EventForm';
import { mockUsers } from '@/modules/calendar/data/mock-users';
import { CalendarEvent } from '@/modules/calendar/types';
import { Button, Typography } from '@/shared/components/common';

import { ComponentDemo } from '../../components';

/**
 * Trang demo cho EventForm
 */
const EventFormPage: React.FC = () => {
  const { t } = useTranslation();
  const [showModal, setShowModal] = useState(false);
  const [event, setEvent] = useState<CalendarEvent>({
    id: '1',
    title: 'Họp nhóm',
    start: new Date(new Date().setHours(10, 0, 0, 0)),
    end: new Date(new Date().setHours(12, 0, 0, 0)),
    description: '<PERSON>uộ<PERSON> họp nhóm hàng tuần để thảo luận về tiến độ dự án',
    location: 'Phòng họp A',
    className: 'calendar-event-meeting',
    extendedProps: {
      type: 'meeting',
      participants: [mockUsers[0], mockUsers[1]],
      reminders: [
        { time: 15, unit: 'minute', method: 'notification' },
        { time: 1, unit: 'hour', method: 'email' },
      ],
      attachments: [
        {
          id: '1',
          name: 'Tài liệu cuộc họp.pdf',
          type: 'application/pdf',
          size: 1024 * 1024,
          url: '#',
        },
      ],
    },
  });

  const eventTypes = [
    { value: 'meeting', label: 'Cuộc họp', color: 'primary' },
    { value: 'appointment', label: 'Cuộc hẹn', color: 'blue-500' },
    { value: 'deadline', label: 'Hạn chót', color: 'red-500' },
    { value: 'lunch', label: 'Ăn trưa', color: 'green-500' },
    { value: 'workshop', label: 'Hội thảo', color: 'yellow-500' },
    { value: 'planning', label: 'Lập kế hoạch', color: 'purple-500' },
  ];

  // Xử lý cập nhật trường của sự kiện
  const handleUpdateField = (field: keyof CalendarEvent, value: unknown) => {
    setEvent(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Xử lý cập nhật loại sự kiện
  const handleUpdateEventType = (
    type: 'meeting' | 'appointment' | 'deadline' | 'lunch' | 'workshop' | 'planning'
  ) => {
    const className = `calendar-event-${type}`;
    setEvent(prev => ({
      ...prev,
      className,
      extendedProps: {
        ...prev.extendedProps,
        type,
      },
    }));
  };

  // Xử lý lưu sự kiện
  const handleSaveEvent = () => {
    console.log('Đã lưu sự kiện:', event);
    setShowModal(false);
  };

  return (
    <div className="p-4 sm:p-6 space-y-8">
      <div>
        <h1 className="text-2xl font-semibold mb-2 text-foreground">
          {t('components.calendar.eventForm.title', 'Event Form')}
        </h1>
        <p className="text-muted">
          {t(
            'components.calendar.eventForm.description',
            'Form thêm và chỉnh sửa sự kiện lịch với nhiều tùy chọn'
          )}
        </p>
      </div>

      <ComponentDemo
        title={t('components.calendar.eventForm.example.title', 'Form sự kiện')}
        description={t(
          'components.calendar.eventForm.example.description',
          'Form thêm/sửa sự kiện với các trường cơ bản và nâng cao'
        )}
        code={`import EventForm from '@/modules/calendar/components/EventForm';
import { CalendarEvent } from '@/modules/calendar/types';

// State cho sự kiện
const [event, setEvent] = useState<CalendarEvent>({
  id: '1',
  title: 'Họp nhóm',
  start: new Date(),
  end: new Date(new Date().setHours(new Date().getHours() + 2)),
  description: 'Cuộc họp nhóm hàng tuần',
  location: 'Phòng họp A',
  className: 'calendar-event-meeting',
  extendedProps: {
    type: 'meeting',
    participants: [/* danh sách người tham gia */],
    reminders: [/* danh sách nhắc nhở */],
    attachments: [/* danh sách tệp đính kèm */]
  }
});

// Danh sách loại sự kiện
const eventTypes = [
  { value: 'meeting', label: 'Cuộc họp', color: 'primary' },
  { value: 'appointment', label: 'Cuộc hẹn', color: 'blue-500' },
  { value: 'deadline', label: 'Hạn chót', color: 'red-500' },
  // ...
];

// Xử lý cập nhật trường của sự kiện
const handleUpdateField = (field: keyof CalendarEvent, value: unknown) => {
  setEvent(prev => ({
    ...prev,
    [field]: value
  }));
};

// Xử lý cập nhật loại sự kiện
const handleUpdateEventType = (type: string) => {
  setEvent(prev => ({
    ...prev,
    className: \`calendar-event-\${type}\`,
    extendedProps: {
      ...prev.extendedProps,
      type
    }
  }));
};

// Sử dụng component
<EventForm
  event={event}
  eventTypes={eventTypes}
  isOpen={showModal}
  title="Thêm sự kiện"
  onClose={() => setShowModal(false)}
  onSave={handleSaveEvent}
  onUpdateField={handleUpdateField}
  onUpdateEventType={handleUpdateEventType}
/>`}
      >
        <div className="space-y-4">
          <Button onClick={() => setShowModal(true)}>
            {t('components.calendar.eventForm.showForm', 'Hiển thị form sự kiện')}
          </Button>

          {showModal && (
            <EventForm
              event={event}
              eventTypes={eventTypes}
              isOpen={showModal}
              title={t('components.calendar.eventForm.addEvent', 'Thêm sự kiện')}
              onClose={() => setShowModal(false)}
              onSave={handleSaveEvent}
              onUpdateField={handleUpdateField}
              onUpdateEventType={handleUpdateEventType}
            />
          )}
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.calendar.eventForm.usage.title', 'Cách sử dụng')}
        description={t(
          'components.calendar.eventForm.usage.description',
          'Hướng dẫn cách sử dụng component EventForm'
        )}
        code={`// Import component
import EventForm from '@/modules/calendar/components/EventForm';

// Sử dụng component
<EventForm
  event={event}                  // Sự kiện đang được chỉnh sửa hoặc tạo mới
  eventTypes={eventTypes}        // Danh sách các loại sự kiện
  isOpen={showModal}             // Trạng thái hiển thị của modal
  title="Thêm sự kiện"           // Tiêu đề của modal
  onClose={() => setShowModal(false)}  // Callback khi đóng modal
  onSave={handleSaveEvent}       // Callback khi lưu sự kiện
  onUpdateField={handleUpdateField}    // Callback khi cập nhật trường của sự kiện
  onUpdateEventType={handleUpdateEventType}  // Callback khi cập nhật loại sự kiện
  users={users}                  // Danh sách người dùng (tùy chọn)
/>`}
      >
        <div className="space-y-4">
          <Typography variant="h6">Props</Typography>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Prop
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Required
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Description
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    event
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    CalendarEvent
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    Yes
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Sự kiện đang được chỉnh sửa hoặc tạo mới
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    eventTypes
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    Array
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    Yes
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Danh sách các loại sự kiện
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    isOpen
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    boolean
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    Yes
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Trạng thái hiển thị của modal
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    onUpdateField
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    Function
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    Yes
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Callback khi cập nhật trường của sự kiện
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default EventFormPage;
