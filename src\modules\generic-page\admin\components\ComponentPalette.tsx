import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Input,
  Button,
  Tabs,
  ScrollArea,
  Icon,
  Typography,
} from '@/shared/components/common';

import { ComponentCategories, ComponentInfo } from '../../types/generic-page.types';

interface ComponentPaletteProps {
  onAddComponent: (componentId: string) => void;
}

/**
 * Component categories with their respective components
 */
const componentCategories: ComponentCategories = {
  form: [
    {
      id: 'text-input',
      label: 'Text Input',
      icon: <Icon name="form-input" size="sm" />,
      description: 'Single line text input field',
    },
    {
      id: 'text-area',
      label: 'Text Area',
      icon: <Icon name="text" size="sm" />,
      description: 'Multi-line text input field',
    },
    {
      id: 'select-dropdown',
      label: 'Select Dropdown',
      icon: <Icon name="list-filter" size="sm" />,
      description: 'Dropdown selection field',
    },
    {
      id: 'checkbox-group',
      label: 'Checkbox Group',
      icon: <Icon name="check-square" size="sm" />,
      description: 'Multiple selection checkboxes',
    },
    {
      id: 'radio-group',
      label: 'Radio Group',
      icon: <Icon name="circle" size="sm" />,
      description: 'Single selection radio buttons',
    },
    {
      id: 'date-picker',
      label: 'Date Picker',
      icon: <Icon name="calendar" size="sm" />,
      description: 'Date selection field',
    },
    {
      id: 'file-upload',
      label: 'File Upload',
      icon: <Icon name="upload" size="sm" />,
      description: 'File upload field',
    },
    {
      id: 'button-group',
      label: 'Button Group',
      icon: <Icon name="mouse-pointer" size="sm" />,
      description: 'Group of buttons',
    },
    {
      id: 'toggle',
      label: 'Toggle Switch',
      icon: <Icon name="toggle-right" size="sm" />,
      description: 'On/off toggle switch',
    },
  ],
  display: [
    {
      id: 'card',
      label: 'Card',
      icon: <Icon name="square" size="sm" />,
      description: 'Container with header and content',
    },
    {
      id: 'stat-card',
      label: 'Stat Card',
      icon: <Icon name="bar-chart-2" size="sm" />,
      description: 'Card displaying a statistic',
    },
    {
      id: 'chart',
      label: 'Chart',
      icon: <Icon name="pie-chart" size="sm" />,
      description: 'Data visualization chart',
    },
    {
      id: 'table',
      label: 'Table',
      icon: <Icon name="grid" size="sm" />,
      description: 'Tabular data display',
    },
    {
      id: 'image',
      label: 'Image',
      icon: <Icon name="image" size="sm" />,
      description: 'Image display',
    },
    {
      id: 'video',
      label: 'Video',
      icon: <Icon name="video" size="sm" />,
      description: 'Video player',
    },
    {
      id: 'html-content',
      label: 'HTML Content',
      icon: <Icon name="code" size="sm" />,
      description: 'Custom HTML content',
    },
    {
      id: 'markdown-content',
      label: 'Markdown',
      icon: <Icon name="file-text" size="sm" />,
      description: 'Markdown content',
    },
    {
      id: 'typography',
      label: 'Typography',
      icon: <Icon name="type" size="sm" />,
      description: 'Text display with styling',
    },
    {
      id: 'alert',
      label: 'Alert',
      icon: <Icon name="alert-circle" size="sm" />,
      description: 'Notification message',
    },
    {
      id: 'divider',
      label: 'Divider',
      icon: <Icon name="minus" size="sm" />,
      description: 'Horizontal divider line',
    },
    {
      id: 'spacer',
      label: 'Spacer',
      icon: <Icon name="maximize" size="sm" />,
      description: 'Empty space for layout',
    },
    {
      id: 'icon',
      label: 'Icon',
      icon: <Icon name="star" size="sm" />,
      description: 'Icon display',
    },
  ],
  layout: [
    {
      id: 'row',
      label: 'Row',
      icon: <Icon name="align-justify" size="sm" />,
      description: 'Horizontal container',
    },
    {
      id: 'column',
      label: 'Column',
      icon: <Icon name="align-center" size="sm" />,
      description: 'Vertical container',
    },
    {
      id: 'tabs',
      label: 'Tabs',
      icon: <Icon name="layers" size="sm" />,
      description: 'Tabbed content container',
    },
    {
      id: 'accordion',
      label: 'Accordion',
      icon: <Icon name="chevrons-down" size="sm" />,
      description: 'Collapsible content sections',
    },
    {
      id: 'container',
      label: 'Container',
      icon: <Icon name="box" size="sm" />,
      description: 'Generic container',
    },
  ],
};

/**
 * Component palette for the page builder
 * Displays available components categorized by type
 */
const ComponentPalette: React.FC<ComponentPaletteProps> = ({ onAddComponent }) => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('form');
  const [recentlyUsed, setRecentlyUsed] = useState<string[]>([]);

  // Filter components based on search term
  const filterComponents = (components: ComponentInfo[]) => {
    if (!searchTerm) {return components;}
    return components.filter(
      component =>
        component.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        component.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (component.description &&
          component.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  };

  // Handle adding a component
  const handleAddComponent = (componentId: string) => {
    onAddComponent(componentId);

    // Update recently used components
    setRecentlyUsed(prev => {
      return [componentId, ...prev.filter(id => id !== componentId)].slice(0, 5);
    });
  };

  return (
    <Card className="w-full">
      <div className="p-4 border-b">
        <Typography variant="h3">
          {t('genericPage.componentPalette.title', 'Component Palette')}
        </Typography>
        <Typography variant="body2" className="text-gray-500">
          {t('genericPage.componentPalette.description', 'Add components to build your layout')}
        </Typography>
        <div className="mt-2">
          <div className="relative">
            <Input
              placeholder={t(
                'genericPage.componentPalette.searchPlaceholder',
                'Search components...'
              )}
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="w-full pl-9"
              leftIcon="search"
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                onClick={() => setSearchTerm('')}
              >
                <Icon name="x" size="sm" />
              </Button>
            )}
          </div>
        </div>
      </div>

      <Tabs
        items={[
          {
            key: 'recent',
            label: t('genericPage.componentPalette.recentTab', 'Recent'),
            disabled: recentlyUsed.length === 0,
            children: (
              <ScrollArea className="h-[300px] pr-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {recentlyUsed.map(id => {
                    // Find component info from all categories
                    let componentInfo: ComponentInfo | undefined;
                    for (const category in componentCategories) {
                      componentInfo = componentCategories[category].find(c => c.id === id);
                      if (componentInfo) {break;}
                    }

                    if (!componentInfo) {return null;}

                    return (
                      <Button
                        key={id}
                        variant="outline"
                        className="justify-start h-auto py-3 w-full hover:border-primary/50 transition-colors duration-200"
                        onClick={() => handleAddComponent(id)}
                      >
                        {componentInfo.icon}
                        <span className="ml-2">{componentInfo.label}</span>
                      </Button>
                    );
                  })}
                </div>
              </ScrollArea>
            ),
          },
          {
            key: 'form',
            label: t('genericPage.componentPalette.formTab', 'Form'),
            children: (
              <ScrollArea className="h-[300px] pr-4">
                {filterComponents(componentCategories.form).length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    {t(
                      'genericPage.componentPalette.noResults',
                      'No components found matching "{{searchTerm}}"',
                      { searchTerm }
                    )}
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {filterComponents(componentCategories.form).map(component => (
                      <Button
                        key={component.id}
                        variant="outline"
                        className="justify-start h-auto py-3 w-full hover:border-primary/50 transition-colors duration-200"
                        onClick={() => handleAddComponent(component.id)}
                      >
                        {component.icon}
                        <div className="ml-2 text-left">
                          <div>{component.label}</div>
                          {component.description && (
                            <div className="text-xs text-muted-foreground">
                              {component.description}
                            </div>
                          )}
                        </div>
                      </Button>
                    ))}
                  </div>
                )}
              </ScrollArea>
            ),
          },
          {
            key: 'display',
            label: t('genericPage.componentPalette.displayTab', 'Display'),
            children: (
              <ScrollArea className="h-[300px] pr-4">
                {filterComponents(componentCategories.display).length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    {t(
                      'genericPage.componentPalette.noResults',
                      'No components found matching "{{searchTerm}}"',
                      { searchTerm }
                    )}
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {filterComponents(componentCategories.display).map(component => (
                      <Button
                        key={component.id}
                        variant="outline"
                        className="justify-start h-auto py-3 w-full hover:border-primary/50 transition-colors duration-200"
                        onClick={() => handleAddComponent(component.id)}
                      >
                        {component.icon}
                        <div className="ml-2 text-left">
                          <div>{component.label}</div>
                          {component.description && (
                            <div className="text-xs text-muted-foreground">
                              {component.description}
                            </div>
                          )}
                        </div>
                      </Button>
                    ))}
                  </div>
                )}
              </ScrollArea>
            ),
          },
          {
            key: 'layout',
            label: t('genericPage.componentPalette.layoutTab', 'Layout'),
            children: (
              <ScrollArea className="h-[300px] pr-4">
                {filterComponents(componentCategories.layout).length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    {t(
                      'genericPage.componentPalette.noResults',
                      'No components found matching "{{searchTerm}}"',
                      { searchTerm }
                    )}
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {filterComponents(componentCategories.layout).map(component => (
                      <Button
                        key={component.id}
                        variant="outline"
                        className="justify-start h-auto py-3 w-full hover:border-primary/50 transition-colors duration-200"
                        onClick={() => handleAddComponent(component.id)}
                      >
                        {component.icon}
                        <div className="ml-2 text-left">
                          <div>{component.label}</div>
                          {component.description && (
                            <div className="text-xs text-muted-foreground">
                              {component.description}
                            </div>
                          )}
                        </div>
                      </Button>
                    ))}
                  </div>
                )}
              </ScrollArea>
            ),
          },
        ]}
        defaultActiveKey="form"
        activeKey={activeTab}
        onChange={setActiveTab}
      />
    </Card>
  );
};

export default ComponentPalette;
