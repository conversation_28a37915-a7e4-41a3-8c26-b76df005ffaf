import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import UserSelector, { User } from '@/modules/calendar/components/users/UserSelector';
import { mockUsers } from '@/modules/calendar/data/mock-users';
import { Card, Typography } from '@/shared/components/common';

import { ComponentDemo } from '../../components';

/**
 * Trang demo cho UserSelector
 */
const UserSelectorPage: React.FC = () => {
  const { t } = useTranslation();
  const [selectedUsers, setSelectedUsers] = useState<User[]>([mockUsers[0]]);
  const [multiSelectedUsers, setMultiSelectedUsers] = useState<User[]>([
    mockUsers[0],
    mockUsers[2],
  ]);
  const [emptySelection, setEmptySelection] = useState<User[]>([]);

  // Tạo nhóm người dùng mẫu
  const userGroups = [
    {
      id: 'group1',
      name: '<PERSON><PERSON><PERSON><PERSON> Phát triển',
      users: [mockUsers[0], mockUsers[1], mockUsers[3]],
    },
    {
      id: 'group2',
      name: 'Nhóm Thiết kế',
      users: [mockUsers[2], mockUsers[4]],
    },
  ];

  return (
    <div className="p-4 sm:p-6 space-y-8">
      <div>
        <h1 className="text-2xl font-semibold mb-2 text-foreground">
          {t('components.calendar.userSelector.title', 'User Selector')}
        </h1>
        <p className="text-muted">
          {t(
            'components.calendar.userSelector.description',
            'Component chọn người dùng cho sự kiện'
          )}
        </p>
      </div>

      <ComponentDemo
        title={t('components.calendar.userSelector.basic.title', 'Chọn người dùng cơ bản')}
        description={t(
          'components.calendar.userSelector.basic.description',
          'Component chọn một người dùng'
        )}
        code={`import UserSelector, { User } from '@/modules/calendar/components/users/UserSelector';
import { mockUsers } from '@/modules/calendar/data/mock-users';

const [selectedUsers, setSelectedUsers] = useState<User[]>([mockUsers[0]]);

<UserSelector
  selectedUsers={selectedUsers}
  onChange={setSelectedUsers}
  users={mockUsers}
  label="Người tham gia"
/>`}
      >
        <Card className="p-6">
          <UserSelector
            selectedUsers={selectedUsers}
            onChange={setSelectedUsers}
            users={mockUsers}
            label={t('components.calendar.userSelector.label', 'Người tham gia')}
          />

          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
            <Typography variant="h6" className="mb-2">
              {t('components.calendar.userSelector.currentValue', 'Giá trị hiện tại')}:
            </Typography>
            <pre className="text-sm overflow-auto p-2 bg-gray-100 dark:bg-gray-900 rounded">
              {JSON.stringify(selectedUsers, null, 2)}
            </pre>
          </div>
        </Card>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.calendar.userSelector.multiple.title', 'Chọn nhiều người dùng')}
        description={t(
          'components.calendar.userSelector.multiple.description',
          'Component chọn nhiều người dùng'
        )}
        code={`import UserSelector, { User } from '@/modules/calendar/components/users/UserSelector';
import { mockUsers } from '@/modules/calendar/data/mock-users';

const [multiSelectedUsers, setMultiSelectedUsers] = useState<User[]>([mockUsers[0], mockUsers[2]]);

<UserSelector
  selectedUsers={multiSelectedUsers}
  onChange={setMultiSelectedUsers}
  users={mockUsers}
  label="Người tham gia"
  multiple={true}
/>`}
      >
        <Card className="p-6">
          <UserSelector
            selectedUsers={multiSelectedUsers}
            onChange={setMultiSelectedUsers}
            users={mockUsers}
            label={t('components.calendar.userSelector.multipleLabel', 'Người tham gia')}
            multiple={true}
          />

          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
            <Typography variant="h6" className="mb-2">
              {t('components.calendar.userSelector.currentValue', 'Giá trị hiện tại')}:
            </Typography>
            <pre className="text-sm overflow-auto p-2 bg-gray-100 dark:bg-gray-900 rounded">
              {JSON.stringify(multiSelectedUsers, null, 2)}
            </pre>
          </div>
        </Card>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.calendar.userSelector.groups.title', 'Chọn người dùng với nhóm')}
        description={t(
          'components.calendar.userSelector.groups.description',
          'Component chọn người dùng với các nhóm người dùng'
        )}
        code={`import UserSelector, { User } from '@/modules/calendar/components/users/UserSelector';
import { mockUsers } from '@/modules/calendar/data/mock-users';

const [selectedUsers, setSelectedUsers] = useState<User[]>([]);

// Tạo nhóm người dùng
const userGroups = [
  {
    id: 'group1',
    name: 'Nhóm Phát triển',
    users: [mockUsers[0], mockUsers[1], mockUsers[3]]
  },
  {
    id: 'group2',
    name: 'Nhóm Thiết kế',
    users: [mockUsers[2], mockUsers[4]]
  }
];

<UserSelector
  selectedUsers={selectedUsers}
  onChange={setSelectedUsers}
  users={mockUsers}
  groups={userGroups}
  label="Người tham gia"
  multiple={true}
/>`}
      >
        <Card className="p-6">
          <UserSelector
            selectedUsers={emptySelection}
            onChange={setEmptySelection}
            users={mockUsers}
            groups={userGroups}
            label={t('components.calendar.userSelector.groupsLabel', 'Người tham gia')}
            multiple={true}
          />

          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
            <Typography variant="h6" className="mb-2">
              {t('components.calendar.userSelector.currentValue', 'Giá trị hiện tại')}:
            </Typography>
            <pre className="text-sm overflow-auto p-2 bg-gray-100 dark:bg-gray-900 rounded">
              {JSON.stringify(emptySelection, null, 2)}
            </pre>
          </div>
        </Card>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.calendar.userSelector.usage.title', 'Cách sử dụng')}
        description={t(
          'components.calendar.userSelector.usage.description',
          'Hướng dẫn cách sử dụng component UserSelector'
        )}
        code={`// Import component
import UserSelector, { User } from '@/modules/calendar/components/users/UserSelector';

// State cho danh sách người dùng đã chọn
const [selectedUsers, setSelectedUsers] = useState<User[]>([]);

// Sử dụng component
<UserSelector
  selectedUsers={selectedUsers}   // Danh sách người dùng đã chọn
  onChange={setSelectedUsers}     // Callback khi thay đổi danh sách
  users={users}                   // Danh sách người dùng
  groups={userGroups}             // Danh sách nhóm người dùng (tùy chọn)
  label="Người tham gia"          // Nhãn hiển thị
  multiple={false}                // Cho phép chọn nhiều người dùng
  disabled={false}                // Trạng thái disabled
  className="custom-class"        // CSS class bổ sung
/>`}
      >
        <div className="space-y-4">
          <Typography variant="h6">Props</Typography>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Prop
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Default
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Description
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    selectedUsers
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    User[]
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    []
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Danh sách người dùng đã chọn
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    onChange
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    Function
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    -
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Callback khi thay đổi danh sách
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    users
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    User[]
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    []
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Danh sách người dùng
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    multiple
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    boolean
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    false
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                    Cho phép chọn nhiều người dùng
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default UserSelectorPage;
