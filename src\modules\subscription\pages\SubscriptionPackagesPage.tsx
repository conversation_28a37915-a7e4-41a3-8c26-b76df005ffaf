/**
 * Trang hiển thị danh sách các gói dịch vụ
 */
import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Typography, ResponsiveGrid, Pagination, Card } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';

import { ServicePackageCard } from '../components';
import { useSubscriptionPackages } from '../hooks';
import { ServiceType, ServicePackage, SubscriptionDuration } from '../types';

/**
 * Trang hiển thị danh sách các gói dịch vụ
 */
const SubscriptionPackagesPage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation('subscription');
  const [selectedType, setSelectedType] = useState<ServiceType>(ServiceType.MAIN);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(6);

  // Sử dụng hook theme
  useTheme();

  // State cho bộ lọc thời hạn
  const [selectedDuration, setSelectedDuration] = useState<SubscriptionDuration>(
    SubscriptionDuration.MONTHLY
  );

  // Lấy dữ liệu gói dịch vụ
  const { packages } = useSubscriptionPackages(selectedType);

  // Lọc gói dịch vụ theo từ khóa tìm kiếm và thời hạn
  const filteredPackages = useMemo(() => {
    return packages.filter(
      pkg =>
        pkg.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pkg.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pkg.features.some(feature => feature.name.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [packages, searchTerm]);

  // Phân trang dữ liệu
  const paginatedPackages = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredPackages.slice(startIndex, endIndex);
  }, [filteredPackages, currentPage, itemsPerPage]);

  // Tính tổng số trang
  const totalPages = useMemo(() => {
    return Math.ceil(filteredPackages.length / itemsPerPage);
  }, [filteredPackages, itemsPerPage]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Xử lý thay đổi số lượng item trên trang
  const handleItemsPerPageChange = useCallback((value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1); // Reset về trang đầu tiên khi thay đổi số lượng item
  }, []);

  // Xử lý khi chọn gói
  const handleSelectPackage = useCallback(
    (pkg: ServicePackage) => {
      console.log('Selected package:', pkg);
      // Chuyển hướng đến trang đơn hàng với thông tin gói
      navigate('/subscription/order', {
        state: {
          packageInfo: {
            id: pkg.id,
            name: pkg.name,
            type: pkg.type,
            price: pkg.prices[selectedDuration],
            duration: selectedDuration,
          },
        },
      });
    },
    [navigate, selectedDuration]
  );

  return (
    <div>
      {/* Thanh công cụ tìm kiếm và lọc */}
      <div className="mb-6">
        <MenuIconBar
          onSearch={setSearchTerm}
          items={[
            // Bộ lọc loại gói
            {
              id: 'type-divider',
              divider: true,
            },
            {
              id: 'all-types',
              label: t('common.all', 'Tất cả loại gói'),
              icon: 'list',
              onClick: () => setSelectedType(ServiceType.MAIN),
            },
            {
              id: 'main',
              label: t('subscription:types.main', 'Gói chính'),
              icon: 'package',
              onClick: () => setSelectedType(ServiceType.MAIN),
            },
            {
              id: 'feature',
              label: t('subscription:types.feature', 'Tính năng'),
              icon: 'layers',
              onClick: () => setSelectedType(ServiceType.FEATURE),
            },

            // Bộ lọc thời hạn
            {
              id: 'duration-divider',
              divider: true,
            },
            {
              id: 'monthly',
              label: t('subscription:duration.monthly', 'Hàng tháng'),
              icon: 'calendar',
              onClick: () => setSelectedDuration(SubscriptionDuration.MONTHLY),
            },
            {
              id: 'semi_annual',
              label: t('subscription:duration.semi_annual', '6 tháng'),
              icon: 'calendar',
              onClick: () => setSelectedDuration(SubscriptionDuration.SEMI_ANNUAL),
            },
            {
              id: 'annual',
              label: t('subscription:duration.annual', 'Hàng năm'),
              icon: 'calendar',
              onClick: () => setSelectedDuration(SubscriptionDuration.ANNUAL),
            },
          ]}
        />
      </div>

      {/* Danh sách gói dịch vụ */}
      <div className="mb-6">
        <ResponsiveGrid
          maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 3 }}
          maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
          gap={6}
        >
          {paginatedPackages.map(pkg => (
            <ServicePackageCard
              key={pkg.id}
              package={pkg}
              duration={selectedDuration}
              onSelect={handleSelectPackage}
            />
          ))}
        </ResponsiveGrid>
      </div>

      {/* Phân trang */}
      {filteredPackages.length > 0 ? (
        <div className="flex justify-end mt-8">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            itemsPerPage={itemsPerPage}
            totalItems={filteredPackages.length}
            showItemsPerPageSelector={true}
            itemsPerPageOptions={[3, 6, 9, 12]}
            variant="compact"
            borderless={false}
          />
        </div>
      ) : (
        <Card className="p-8 text-center">
          <Typography variant="h4" className="text-muted mb-2">
            {t('subscription:noResults', 'Không tìm thấy gói dịch vụ nào')}
          </Typography>
          <Typography variant="body2" className="text-muted">
            {t('subscription:tryDifferentSearch', 'Vui lòng thử tìm kiếm khác')}
          </Typography>
        </Card>
      )}
    </div>
  );
};

export default SubscriptionPackagesPage;
