import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import {
  Form,
  FormItem,
  Input,
  Button,
  ColorPicker,
  Card,
  Typography,
  FormGrid,
} from '@/shared/components/common';

/**
 * Form tạo và chỉnh sửa tag
 */
const TagForm: React.FC = () => {
  const { t } = useTranslation();
  const [color, setColor] = useState('#3B82F6');

  // Schema validation
  const tagSchema = z.object({
    name: z.string().min(1, 'Tên tag là bắt buộc'),
    slug: z.string().min(1, 'Slug là bắt buộc'),
    description: z.string().optional(),
  });

  // Xử lý khi submit form
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleSubmit = (data: any) => {
    // Thêm color vào values
    const tagData = {
      ...data,
      color,
    };

    console.log('Tag data:', tagData);
    // Gọi API tạo/cập nhật tag ở đây
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <Typography variant="h4" className="mb-4">
        {t('components.tagForm.title', 'Tạo Tag Mới')}
      </Typography>

      <Form
        schema={tagSchema}
        onSubmit={handleSubmit}
        defaultValues={{
          name: '',
          slug: '',
          description: '',
        }}
        className="space-y-4"
      >
        <FormGrid columns={2} gap="md">
          <FormItem name="name" label={t('components.tagForm.name', 'Tên Tag')} required>
            <Input
              placeholder={t('components.tagForm.namePlaceholder', 'Nhập tên tag')}
              fullWidth
            />
          </FormItem>

          <FormItem name="slug" label={t('components.tagForm.slug', 'Slug')} required>
            <Input
              placeholder={t('components.tagForm.slugPlaceholder', 'nhap-ten-tag')}
              fullWidth
            />
          </FormItem>
        </FormGrid>

        <FormItem name="description" label={t('components.tagForm.description', 'Mô tả')}>
          <Input
            placeholder={t('components.tagForm.descriptionPlaceholder', 'Nhập mô tả tag')}
            fullWidth
          />
        </FormItem>

        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">
            {t('components.tagForm.color', 'Màu sắc')}
          </label>
          <ColorPicker
            value={color}
            onChange={setColor}
            presetColors={[
              '#3B82F6', // blue
              '#10B981', // green
              '#F59E0B', // yellow
              '#EF4444', // red
              '#8B5CF6', // purple
              '#EC4899', // pink
              '#6B7280', // gray
              '#000000', // black
            ]}
          />
        </div>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" type="button">
            {t('common.cancel', 'Hủy')}
          </Button>
          <Button variant="primary" type="submit">
            {t('common.save', 'Lưu')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default TagForm;
