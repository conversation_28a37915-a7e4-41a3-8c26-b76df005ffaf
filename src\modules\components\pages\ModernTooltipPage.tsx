import React from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Tooltip, IconCard } from '@/shared/components/common';

import ComponentDemo from '../components/ComponentDemo';

const ModernTooltipPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="p-4 sm:p-6 space-y-8">
      <div>
        <h1 className="text-2xl font-semibold mb-2 text-foreground">
          {t('components.tooltip.title', 'Tooltip')}
        </h1>
        <p className="text-muted">
          {t(
            'components.tooltip.description',
            'Tooltip component with various positions and styles.'
          )}
        </p>
      </div>

      <ComponentDemo
        title={t('components.tooltip.basic.title', 'Basic Tooltip')}
        description={t(
          'components.tooltip.basic.description',
          'Basic tooltip with default settings.'
        )}
        code={`<Tooltip content="This is a tooltip">
  <Button>Hover me</Button>
</Tooltip>`}
      >
        <div className="flex flex-wrap gap-4">
          <Tooltip content={t('components.tooltip.basic.content', 'This is a tooltip')}>
            <Button>{t('components.tooltip.basic.button', 'Hover me')}</Button>
          </Tooltip>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.tooltip.positions.title', 'Tooltip Positions')}
        description={t(
          'components.tooltip.positions.description',
          'Tooltips can be displayed in different positions.'
        )}
        code={`<Tooltip content="Top tooltip" position="top">
  <Button>Top</Button>
</Tooltip>

<Tooltip content="Right tooltip" position="right">
  <Button>Right</Button>
</Tooltip>

<Tooltip content="Bottom tooltip" position="bottom">
  <Button>Bottom</Button>
</Tooltip>

<Tooltip content="Left tooltip" position="left">
  <Button>Left</Button>
</Tooltip>`}
      >
        <div className="flex flex-wrap gap-4">
          <Tooltip content={t('components.tooltip.positions.top', 'Top tooltip')} position="top">
            <Button>{t('components.tooltip.positions.topButton', 'Top')}</Button>
          </Tooltip>

          <Tooltip
            content={t('components.tooltip.positions.right', 'Right tooltip')}
            position="right"
          >
            <Button>{t('components.tooltip.positions.rightButton', 'Right')}</Button>
          </Tooltip>

          <Tooltip
            content={t('components.tooltip.positions.bottom', 'Bottom tooltip')}
            position="bottom"
          >
            <Button>{t('components.tooltip.positions.bottomButton', 'Bottom')}</Button>
          </Tooltip>

          <Tooltip content={t('components.tooltip.positions.left', 'Left tooltip')} position="left">
            <Button>{t('components.tooltip.positions.leftButton', 'Left')}</Button>
          </Tooltip>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.tooltip.variants.title', 'Tooltip Variants')}
        description={t(
          'components.tooltip.variants.description',
          'Tooltips with different styles.'
        )}
        code={`<Tooltip content="Dark tooltip" className="bg-gray-800 text-white">
  <Button>Dark</Button>
</Tooltip>

<Tooltip content="Light tooltip" className="bg-white text-gray-800">
  <Button>Light</Button>
</Tooltip>`}
      >
        <div className="flex flex-wrap gap-4">
          <Tooltip
            content={t('components.tooltip.variants.dark.content', 'Dark tooltip')}
            className="bg-gray-800 text-white"
          >
            <Button>{t('components.tooltip.variants.dark.button', 'Dark')}</Button>
          </Tooltip>

          <Tooltip
            content={t('components.tooltip.variants.light.content', 'Light tooltip')}
            className="bg-white text-gray-800"
          >
            <Button>{t('components.tooltip.variants.light.button', 'Light')}</Button>
          </Tooltip>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.tooltip.sizes.title', 'Tooltip Sizes')}
        description={t('components.tooltip.sizes.description', 'Tooltips in different sizes.')}
        code={`<Tooltip content="Small tooltip" className="text-xs py-1 px-2">
  <Button>Small</Button>
</Tooltip>

<Tooltip content="Medium tooltip" className="text-sm py-1.5 px-3">
  <Button>Medium</Button>
</Tooltip>

<Tooltip content="Large tooltip" className="text-base py-2 px-4">
  <Button>Large</Button>
</Tooltip>`}
      >
        <div className="flex flex-wrap gap-4">
          <Tooltip
            content={t('components.tooltip.sizes.small.content', 'Small tooltip')}
            className="text-xs py-1 px-2"
          >
            <Button>{t('common.small', 'Small')}</Button>
          </Tooltip>

          <Tooltip
            content={t('components.tooltip.sizes.medium.content', 'Medium tooltip')}
            className="text-sm py-1.5 px-3"
          >
            <Button>{t('common.medium', 'Medium')}</Button>
          </Tooltip>

          <Tooltip
            content={t('components.tooltip.sizes.large.content', 'Large tooltip')}
            className="text-base py-2 px-4"
          >
            <Button>{t('common.large', 'Large')}</Button>
          </Tooltip>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.tooltip.withIcons.title', 'Tooltip with Icons')}
        description={t(
          'components.tooltip.withIcons.description',
          'Tooltips used with icon buttons.'
        )}
        code={`<Tooltip content="Add new item" position="bottom">
  <IconCard icon="plus" variant="primary" onClick={() => {}} />
</Tooltip>

<Tooltip content="Search" position="bottom">
  <IconCard icon="search" variant="default" onClick={() => {}} />
</Tooltip>

<Tooltip content="Filter" position="bottom">
  <IconCard icon="filter" variant="default" onClick={() => {}} />
</Tooltip>`}
      >
        <div className="flex flex-wrap gap-4">
          <Tooltip
            content={t('components.tooltip.withIcons.add', 'Add new item')}
            position="bottom"
          >
            <IconCard icon="plus" variant="primary" onClick={() => {}} />
          </Tooltip>

          <Tooltip content={t('common.search', 'Search')} position="bottom">
            <IconCard icon="search" variant="default" onClick={() => {}} />
          </Tooltip>

          <Tooltip content={t('common.filter', 'Filter')} position="bottom">
            <IconCard icon="filter" variant="default" onClick={() => {}} />
          </Tooltip>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.tooltip.noArrow.title', 'Tooltip without Arrow')}
        description={t(
          'components.tooltip.noArrow.description',
          'Tooltips can be displayed without an arrow.'
        )}
        code={`<Tooltip content="Tooltip without arrow" className="rounded-md">
  <Button>No Arrow</Button>
</Tooltip>`}
      >
        <div className="flex flex-wrap gap-4">
          <Tooltip
            content={t('components.tooltip.noArrow.content', 'Tooltip without arrow')}
            className="rounded-md"
          >
            <Button>{t('components.tooltip.noArrow.button', 'No Arrow')}</Button>
          </Tooltip>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default ModernTooltipPage;
